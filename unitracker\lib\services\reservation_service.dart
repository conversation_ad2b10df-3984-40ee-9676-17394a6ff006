import 'package:flutter/foundation.dart';
import 'package:unitracker/models/bus_route.dart';
import 'package:unitracker/models/reservation.dart';
import 'package:unitracker/services/notification_service.dart';
import 'package:unitracker/services/notification_settings_service.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/driver_notification.dart';
import '../models/bus_stop.dart';
import 'supabase_service.dart';

class ReservationService extends ChangeNotifier {
  // Singleton instance with NotificationService
  static ReservationService? _instance;
  static ReservationService getInstance(NotificationService notificationService,
      NotificationSettingsService notificationSettingsService) {
    _instance ??= ReservationService._internal(
        notificationService, notificationSettingsService);
    return _instance!;
  }

  final NotificationService _notificationService;
  final NotificationSettingsService _notificationSettingsService;
  final SupabaseService _supabaseService = SupabaseService.instance;
  late final List<Reservation> _reservations;
  static const int maxSeatsPerBus = 12;
  bool _isLoading = false;
  String? _error;

  bool get isLoading => _isLoading;
  String? get error => _error;

  ReservationService._internal(
      this._notificationService, this._notificationSettingsService) {
    _reservations = [];
    loadReservations();
  }

  Future<void> loadReservations() async {
    final currentUser = _supabaseService.currentUser;
    if (currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      final reservationsData = await _supabaseService.getUserReservations(currentUser.id);
      _reservations.clear();
      _reservations.addAll(reservationsData.map((data) => _mapSupabaseToReservation(data)));
      _error = null;
    } catch (e) {
      _error = 'Failed to load reservations: ${e.toString()}';
      debugPrint('Error loading reservations: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Getter for reservations (immutable copy)
  List<Reservation> get reservations => List.unmodifiable(_reservations);

  // Check if time is morning hours (before 12 PM)
  bool _isMorningHours(DateTime time) {
    return time.hour < 12;
  }

  // Check if user has existing reservation for the same time period on the same day
  bool _hasExistingReservation(DateTime newReservationTime) {
    final bool isNewReservationMorning = _isMorningHours(newReservationTime);
    final existingReservations = _reservations.where((reservation) {
      try {
        final existingReservationTime = DateTime.parse(reservation.date);
        // Check if it's the same day
        if (existingReservationTime.year == newReservationTime.year &&
            existingReservationTime.month == newReservationTime.month &&
            existingReservationTime.day == newReservationTime.day) {
          // Check if both reservations are in the same time period (morning/evening)
          final bool isExistingMorning =
              _isMorningHours(existingReservationTime);
          return isExistingMorning == isNewReservationMorning &&
              reservation.status != 'cancelled';
        }
        return false;
      } catch (e) {
        return false;
      }
    });

    return existingReservations.isNotEmpty;
  }

  // Cancel a reservation
  Future<void> cancelReservation(String reservationId) async {
    final currentUser = _supabaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    _isLoading = true;
    notifyListeners();

    try {
      final index = _reservations.indexWhere((r) => r.id == reservationId);
      if (index != -1) {
        final reservation = _reservations[index];
        final now = DateTime.now();
        final reservationDateTime = DateTime.parse(reservation.date);

        // Check if cancellation is allowed (more than 2 hours before departure)
        if (reservationDateTime.difference(now).inHours <= 2) {
          throw Exception(
              'Reservations can only be cancelled more than 2 hours before departure');
        }

        // Cancel in Supabase
        await _supabaseService.cancelReservation(reservationId);

        _reservations[index] = Reservation(
          id: reservation.id,
          route: reservation.route,
          date: reservation.date,
          status: 'cancelled',
          seatNumber: reservation.seatNumber,
          pickupPoint: reservation.pickupPoint,
          dropPoint: reservation.dropPoint,
        );

        // Send cancellation notification if enabled
        if (_notificationSettingsService.reservationUpdatesEnabled) {
          await _supabaseService.createNotification({
            'user_id': currentUser.id,
            'title': 'Reservation Cancelled',
            'message': 'Your reservation for ${DateFormat('MMM d').format(reservationDateTime)} has been cancelled.',
            'type': 'info',
          });

          _notificationService.addNotification(
            title: 'Reservation Cancelled',
            message: 'Your reservation for ${DateFormat('MMM d').format(reservationDateTime)} has been cancelled.',
            type: NotificationType.alert,
          );
        }

        _error = null;
        notifyListeners();
      } else {
        throw Exception('Reservation not found');
      }
    } catch (e) {
      _error = 'Failed to cancel reservation: ${e.toString()}';
      throw Exception('Failed to cancel reservation: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }



  Reservation _createFallbackReservation(BusRoute route, DateTime dateTime, String pickupPoint, String dropPoint, int seatNumber) {
    return Reservation(
      id: 'local-${DateTime.now().millisecondsSinceEpoch}',
      route: route,
      date: dateTime.toIso8601String(),
      pickupPoint: pickupPoint,
      dropPoint: dropPoint,
      seatNumber: seatNumber.toString(),
      status: 'confirmed',
    );
  }

  Reservation _mapSupabaseToReservation(Map<String, dynamic> data) {
    final routeData = data['routes'] as Map<String, dynamic>;
    final pickupStopData = data['pickup_stop'] as Map<String, dynamic>?;
    final dropStopData = data['drop_stop'] as Map<String, dynamic>?;

    final route = BusRoute(
      id: routeData['id'] as String,
      name: routeData['name'] as String,
      pickup: routeData['pickup_location'] as String,
      drop: routeData['drop_location'] as String,
      startTime: _formatTime(routeData['start_time'] as String),
      endTime: _formatTime(routeData['end_time'] as String),
      stops: [], // We'll load stops separately if needed
      iconColor: routeData['color_code'] as String? ?? '2196F3',
    );

    return Reservation(
      id: data['id'] as String,
      route: route,
      date: data['reservation_date'] as String,
      status: data['status'] as String,
      seatNumber: data['seat_number'] as String? ?? '',
      pickupPoint: pickupStopData?['name'] as String? ?? route.pickup,
      dropPoint: dropStopData?['name'] as String? ?? route.drop,
    );
  }

  String _formatTime(String timeString) {
    final parts = timeString.split(':');
    final hour = int.parse(parts[0]);
    final minute = parts[1];

    if (hour == 0) {
      return '12:$minute AM';
    } else if (hour < 12) {
      return '$hour:$minute AM';
    } else if (hour == 12) {
      return '12:$minute PM';
    } else {
      return '${hour - 12}:$minute PM';
    }
  }

  Future<Reservation> createReservation({
    required BusRoute route,
    required String date,
    required String pickupPoint,
    required String dropPoint,
  }) async {
    final currentUser = _supabaseService.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    _isLoading = true;
    notifyListeners();

    try {
      // Parse the reservation date and time
      final reservationDateTime = DateTime.parse(date);
      final now = DateTime.now();

      // Check if the reservation time has already passed
      if (reservationDateTime.isBefore(now)) {
        throw Exception(
            'Cannot make reservations for past times. Please select a future time.');
      }

      // Check for existing reservation in the same time period
      if (_hasExistingReservation(reservationDateTime)) {
        final period =
            _isMorningHours(reservationDateTime) ? 'morning' : 'evening';
        throw Exception(
            'You already have a reservation for the $period. Only one reservation per time period is allowed.');
      }

      // Get next available seat number
      final seatNumber = await _getNextAvailableSeatNumber(route.id, date);

      // Check if seats are available
      if (seatNumber > maxSeatsPerBus) {
        throw Exception('No seats available for this trip');
      }

      // Find pickup and drop stops
      final pickupStopId = await _findStopId(route.id, pickupPoint);
      final dropStopId = await _findStopId(route.id, dropPoint);

      // Create reservation in Supabase
      final reservationData = await _supabaseService.createReservation({
        'user_id': currentUser.id,
        'route_id': route.id,
        'reservation_date': reservationDateTime.toIso8601String().split('T')[0],
        'pickup_stop_id': pickupStopId,
        'drop_stop_id': dropStopId,
        'seat_number': seatNumber.toString(),
        'status': 'confirmed',
      });

      // Create reservation object (fallback if Supabase fails)
      final reservation = reservationData != null
        ? _mapSupabaseToReservation(reservationData)
        : _createFallbackReservation(route, reservationDateTime, pickupPoint, dropPoint, seatNumber);
      _reservations.add(reservation);

      // Send notification if enabled
      if (_notificationSettingsService.reservationUpdatesEnabled) {
        await _supabaseService.createNotification({
          'user_id': currentUser.id,
          'title': 'Reservation Confirmed',
          'message': 'Your reservation for ${DateFormat('MMM d').format(reservationDateTime)} has been confirmed.',
          'type': 'success',
        });

        _notificationService.addNotification(
          title: 'Reservation Confirmed',
          message: 'Your reservation for ${DateFormat('MMM d').format(reservationDateTime)} has been confirmed.',
          type: NotificationType.alert,
        );
      }

      _error = null;
      notifyListeners();
      return reservation;
    } catch (e) {
      _error = 'Failed to create reservation: ${e.toString()}';
      throw Exception('Failed to create reservation: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<String?> _findStopId(String routeId, String stopName) async {
    try {
      final routeData = await _supabaseService.getRoute(routeId);
      if (routeData != null) {
        final stops = routeData['bus_stops'] as List<dynamic>? ?? [];
        for (final stop in stops) {
          if (stop['name'] == stopName) {
            return stop['id'] as String;
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error finding stop ID: $e');
      return null;
    }
  }

  Future<int> _getNextAvailableSeatNumber(String routeId, String date) async {
    try {
      // This would need a custom query to count reservations for the specific route and date
      // For now, we'll use a simple counter based on existing reservations
      final existingReservations = _reservations.where((r) =>
        r.route.id == routeId &&
        r.date.split('T')[0] == date.split('T')[0] &&
        r.status != 'cancelled'
      ).length;

      return existingReservations + 1;
    } catch (e) {
      debugPrint('Error getting seat number: $e');
      return 1;
    }
  }



  int getRemainingSeats(String routeId, String dateTime) {
    final reservations = _reservations
        .where((r) =>
            r.route.id == routeId &&
            r.date == dateTime &&
            r.status != 'cancelled')
        .length;

    return maxSeatsPerBus - reservations;
  }

  List<Reservation> getCurrentReservations() {
    final now = DateTime.now();
    return _reservations.where((reservation) {
      try {
        final reservationDate = DateTime.parse(reservation.date);
        return reservationDate.isAfter(now) &&
            reservation.status != 'cancelled';
      } catch (e) {
        return false;
      }
    }).toList();
  }

  List<Reservation> getReservationHistory() {
    final now = DateTime.now();
    return _reservations.where((reservation) {
      try {
        final reservationDate = DateTime.parse(reservation.date);
        return reservationDate.isBefore(now) ||
            reservation.status == 'cancelled';
      } catch (e) {
        return false;
      }
    }).toList();
  }

  // Remove test reservations method as it's no longer needed
  void addTestReservations() {
    // No longer adding test reservations
    notifyListeners();
  }
}
