<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A University Bus Tracking App">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="unitracker">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>unitracker</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <!-- Initialize Google Maps with callback -->
  <script>
    function initGoogleMaps() {
      console.log('Google Maps API loaded successfully');
      // Load Flutter app after Google Maps is ready
      var script = document.createElement('script');
      script.src = 'flutter_bootstrap.js';
      document.body.appendChild(script);
    }

    // Fallback if callback doesn't work
    function initializeApp() {
      if (typeof google !== 'undefined' && google.maps) {
        console.log('Google Maps API loaded via fallback');
        var script = document.createElement('script');
        script.src = 'flutter_bootstrap.js';
        document.body.appendChild(script);
      } else {
        console.log('Waiting for Google Maps API...');
        setTimeout(initializeApp, 200);
      }
    }

    // Start fallback timer
    setTimeout(initializeApp, 3000);
  </script>

  <!-- Google Maps JavaScript API with callback - Temporarily disabled to avoid billing errors -->
  <!-- <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBrsMZk0tAIpmjIjyhAZdPZAdEGvCmS_Lw&libraries=geometry,places&callback=initGoogleMaps"></script> -->
</body>
</html>
