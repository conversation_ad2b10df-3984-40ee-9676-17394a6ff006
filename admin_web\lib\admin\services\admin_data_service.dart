import 'package:supabase_flutter/supabase_flutter.dart';

class AdminDataService {
  static final AdminDataService _instance = AdminDataService._internal();
  factory AdminDataService() => _instance;
  AdminDataService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;

  // Dashboard Statistics
  Future<Map<String, dynamic>> getDashboardStats() async {
    try {
      // Get all buses count
      final allBusesResponse = await _supabase
          .from('buses')
          .select('id, status');

      // Get active buses count
      final activeBuses = allBusesResponse.where((bus) => bus['status'] == 'active').length;

      // Get total routes count
      final routesResponse = await _supabase
          .from('routes')
          .select('id, is_active')
          .eq('is_active', true);

      // Get total drivers count
      final driversResponse = await _supabase
          .from('drivers')
          .select('id, is_active');

      return {
        'activeBuses': activeBuses,
        'totalBuses': allBusesResponse.length,
        'totalRoutes': routesResponse.length,
        'totalDrivers': driversResponse.length,
        'onTimeRate': 94,
        'issues': 2,
      };
    } catch (e) {
      // Return fallback data if Supabase fails
      return {
        'activeBuses': 12,
        'totalBuses': 15,
        'totalRoutes': 8,
        'totalDrivers': 6,
        'onTimeRate': 94,
        'issues': 2,
      };
    }
  }

  // Bus Management
  Future<List<Map<String, dynamic>>> getAllBuses() async {
    try {
      final response = await _supabase
          .from('buses')
          .select('''
            id,
            bus_number,
            plate_number,
            capacity,
            status,
            driver_id,
            route_id,
            created_at,
            updated_at,
            routes(
              id,
              name,
              pickup_location,
              drop_location
            )
          ''');

      return response.map<Map<String, dynamic>>((bus) {
        final route = bus['routes'];
        return {
          'id': bus['id'],
          'busNumber': bus['bus_number'],
          'plateNumber': bus['plate_number'],
          'capacity': bus['capacity'],
          'status': bus['status'],
          'driverId': bus['driver_id'],
          'routeId': bus['route_id'],
          'routeName': route != null ? route['name'] : 'No Route Assigned',
          'createdAt': bus['created_at'],
          'updatedAt': bus['updated_at'],
        };
      }).toList();
    } catch (e) {
      throw Exception('Failed to load buses: $e');
    }
  }

  // Route Management
  Future<List<Map<String, dynamic>>> getAllRoutes() async {
    try {
      final response = await _supabase
          .from('routes')
          .select('''
            id,
            name,
            pickup_location,
            drop_location,
            start_time,
            end_time,
            is_active,
            color_code,
            created_at,
            updated_at,
            bus_stops(
              id,
              name,
              latitude,
              longitude,
              stop_order,
              estimated_arrival_time
            )
          ''')
          .order('created_at', ascending: false);

      return response.map<Map<String, dynamic>>((route) {
        final stops = route['bus_stops'] as List<dynamic>? ?? [];
        return {
          'id': route['id'],
          'name': route['name'],
          'pickupLocation': route['pickup_location'],
          'dropLocation': route['drop_location'],
          'startTime': route['start_time'],
          'endTime': route['end_time'],
          'status': route['is_active'] == true ? 'active' : 'inactive',
          'colorCode': route['color_code'],
          'stops': stops,
          'createdAt': route['created_at'],
          'updatedAt': route['updated_at'],
        };
      }).toList();
    } catch (e) {
      print('Error fetching routes: $e');
      throw Exception('Failed to load routes: $e');
    }
  }

  // Driver Management
  Future<List<Map<String, dynamic>>> getAllDrivers() async {
    try {
      final response = await _supabase
          .from('drivers')
          .select('''
            id,
            full_name,
            email,
            driver_license,
            license_expiry,
            phone,
            is_active,
            created_at,
            updated_at
          ''')
          .order('created_at', ascending: false);

      return response.map<Map<String, dynamic>>((driver) {
        return {
          'id': driver['id'],
          'fullName': driver['full_name'],
          'email': driver['email'],
          'phone': driver['phone'],
          'driverId': driver['id'], // Use id as driver identifier
          'licenseNumber': driver['driver_license'],
          'licenseExpiration': driver['license_expiry'],
          'isActive': driver['is_active'],
          'assignedBuses': [], // Will be populated separately if needed
          'createdAt': driver['created_at'],
          'updatedAt': driver['updated_at'],
        };
      }).toList();
    } catch (e) {
      print('Error fetching drivers: $e');
      throw Exception('Failed to load drivers: $e');
    }
  }

  // Schedule Management
  Future<List<Map<String, dynamic>>> getAllSchedules() async {
    try {
      final response = await _supabase
          .from('schedules')
          .select('''
            id,
            route_id,
            bus_id,
            departure_time,
            arrival_time,
            days_of_week,
            is_active,
            created_at,
            routes(
              id,
              name,
              pickup_location,
              drop_location
            ),
            buses(
              id,
              bus_number
            )
          ''')
          .order('created_at', ascending: false);

      return response.map<Map<String, dynamic>>((schedule) {
        final route = schedule['routes'];
        final bus = schedule['buses'];
        final daysOfWeek = schedule['days_of_week'] as List<dynamic>? ?? [];
        
        // Convert day integers back to day names
        final dayMap = {
          1: 'Monday', 2: 'Tuesday', 3: 'Wednesday', 4: 'Thursday',
          5: 'Friday', 6: 'Saturday', 7: 'Sunday',
        };
        final dayNames = daysOfWeek.map((day) => dayMap[day] ?? 'Unknown').toList();

        return {
          'id': schedule['id'],
          'routeId': schedule['route_id'],
          'busId': schedule['bus_id'],
          'routeName': route?['name'] ?? 'Unknown Route',
          'busNumber': bus?['bus_number'] ?? 'Unknown Bus',
          'departureTime': schedule['departure_time'],
          'arrivalTime': schedule['arrival_time'],
          'days': dayNames,
          'isActive': schedule['is_active'],
          'createdAt': schedule['created_at'],
        };
      }).toList();
    } catch (e) {
      print('Error fetching schedules: $e');
      throw Exception('Failed to load schedules: $e');
    }
  }

  // Create schedule with driver assignment
  Future<Map<String, dynamic>> createSchedule({
    required String routeId,
    required String busId,
    required String driverId,
    required String departureTime,
    required String arrivalTime,
    required List<String> days,
  }) async {
    try {
      // Convert day names to integers (1=Monday, 2=Tuesday, ..., 7=Sunday)
      final dayMap = {
        'Monday': 1,
        'Tuesday': 2,
        'Wednesday': 3,
        'Thursday': 4,
        'Friday': 5,
        'Saturday': 6,
        'Sunday': 7,
      };

      final dayIntegers = days.map((day) => dayMap[day] ?? 1).toList();

      final response = await _supabase
          .from('schedules')
          .insert({
            'route_id': routeId,
            'bus_id': busId,
            'departure_time': departureTime,
            'arrival_time': arrivalTime,
            'days_of_week': dayIntegers, // Convert to integers
            'is_active': true,
          })
          .select()
          .single();

      // Create driver assignment
      await _supabase
          .from('driver_assignments')
          .insert({
            'driver_id': driverId,
            'route_id': routeId,
            'bus_id': busId,
            'assigned_date': DateTime.now().toIso8601String().split('T')[0], // Just the date part
            'is_active': true,
            'shift_start_time': departureTime,
            'shift_end_time': arrivalTime,
          });

      return response;
    } catch (e) {
      throw Exception('Failed to create schedule: $e');
    }
  }

  // Update schedule
  Future<Map<String, dynamic>> updateSchedule({
    required String scheduleId,
    String? routeId,
    String? busId,
    String? departureTime,
    String? arrivalTime,
    List<String>? days,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      if (routeId != null) updateData['route_id'] = routeId;
      if (busId != null) updateData['bus_id'] = busId;
      if (departureTime != null) updateData['departure_time'] = departureTime;
      if (arrivalTime != null) updateData['arrival_time'] = arrivalTime;

      if (days != null) {
        final dayMap = {
          'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 'Thursday': 4,
          'Friday': 5, 'Saturday': 6, 'Sunday': 7,
          'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4,
          'Fri': 5, 'Sat': 6, 'Sun': 7,
        };
        final dayIntegers = days.map((day) => dayMap[day] ?? 1).toList();
        updateData['days_of_week'] = dayIntegers;
      }

      final response = await _supabase
          .from('schedules')
          .update(updateData)
          .eq('id', scheduleId)
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('Failed to update schedule: $e');
    }
  }

  // Delete schedule
  Future<void> deleteSchedule(String scheduleId) async {
    try {
      await _supabase
          .from('schedules')
          .delete()
          .eq('id', scheduleId);
    } catch (e) {
      throw Exception('Failed to delete schedule: $e');
    }
  }

  // Bus CRUD operations
  Future<Map<String, dynamic>> createBus({
    required String busNumber,
    required String plateNumber,
    required int capacity,
    String status = 'active',
    String? driverId,
    String? routeId,
  }) async {
    try {
      final response = await _supabase
          .from('buses')
          .insert({
            'bus_number': busNumber,
            'plate_number': plateNumber,
            'capacity': capacity,
            'status': status,
            'driver_id': driverId,
            'route_id': routeId,
          })
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('Failed to create bus: $e');
    }
  }

  Future<Map<String, dynamic>> updateBus({
    required String busId,
    String? busNumber,
    String? plateNumber,
    int? capacity,
    String? status,
    String? driverId,
    String? routeId,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      if (busNumber != null) updateData['bus_number'] = busNumber;
      if (plateNumber != null) updateData['plate_number'] = plateNumber;
      if (capacity != null) updateData['capacity'] = capacity;
      if (status != null) updateData['status'] = status;
      if (driverId != null) updateData['driver_id'] = driverId;
      if (routeId != null) updateData['route_id'] = routeId;

      final response = await _supabase
          .from('buses')
          .update(updateData)
          .eq('id', busId)
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('Failed to update bus: $e');
    }
  }

  Future<void> deleteBus(String busId) async {
    try {
      await _supabase
          .from('buses')
          .delete()
          .eq('id', busId);
    } catch (e) {
      throw Exception('Failed to delete bus: $e');
    }
  }

  // Route CRUD operations
  Future<Map<String, dynamic>> createRoute({
    required String name,
    required String pickupLocation,
    required String dropLocation,
    required String startTime,
    required String endTime,
    String status = 'active',
    String? colorCode,
  }) async {
    try {
      final response = await _supabase
          .from('routes')
          .insert({
            'name': name,
            'pickup_location': pickupLocation,
            'drop_location': dropLocation,
            'start_time': startTime,
            'end_time': endTime,
            'is_active': status == 'active',
            'color_code': colorCode ?? '2196F3',
          })
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('Failed to create route: $e');
    }
  }

  Future<Map<String, dynamic>> createRouteWithStops({
    required String name,
    required String pickupLocation,
    required String dropLocation,
    required String startTime,
    required String endTime,
    String status = 'active',
    String? colorCode,
    List<Map<String, dynamic>>? busStops,
  }) async {
    try {
      // First create the route
      final routeResponse = await _supabase
          .from('routes')
          .insert({
            'name': name,
            'pickup_location': pickupLocation,
            'drop_location': dropLocation,
            'start_time': startTime,
            'end_time': endTime,
            'is_active': status == 'active',
            'color_code': colorCode ?? '2196F3',
          })
          .select()
          .single();

      final routeId = routeResponse['id'];

      // Then create bus stops if provided
      if (busStops != null && busStops.isNotEmpty) {
        final stopsToInsert = busStops.map((stop) => {
          'route_id': routeId,
          'name': stop['name'],
          'stop_order': stop['order'],
          'estimated_arrival_time': stop['time'],
          'is_pickup_point': stop['order'] == 1, // First stop is pickup
          'is_drop_point': stop['order'] == busStops.length, // Last stop is drop
        }).toList();

        await _supabase
            .from('bus_stops')
            .insert(stopsToInsert);
      }

      return routeResponse;
    } catch (e) {
      throw Exception('Failed to create route with stops: $e');
    }
  }

  Future<Map<String, dynamic>> updateRouteWithStops({
    required String routeId,
    String? name,
    String? pickupLocation,
    String? dropLocation,
    String? startTime,
    String? endTime,
    String? status,
    String? colorCode,
    List<Map<String, dynamic>>? busStops,
  }) async {
    try {
      // First update the route
      final updateData = <String, dynamic>{};
      if (name != null) updateData['name'] = name;
      if (pickupLocation != null) updateData['pickup_location'] = pickupLocation;
      if (dropLocation != null) updateData['drop_location'] = dropLocation;
      if (startTime != null) updateData['start_time'] = startTime;
      if (endTime != null) updateData['end_time'] = endTime;
      if (status != null) updateData['is_active'] = status == 'active';
      if (colorCode != null) updateData['color_code'] = colorCode;

      final routeResponse = await _supabase
          .from('routes')
          .update(updateData)
          .eq('id', routeId)
          .select()
          .single();

      // Update bus stops if provided
      if (busStops != null) {
        // First delete existing stops
        await _supabase
            .from('bus_stops')
            .delete()
            .eq('route_id', routeId);

        // Then insert new stops
        if (busStops.isNotEmpty) {
          final stopsToInsert = busStops.map((stop) => {
            'route_id': routeId,
            'name': stop['name'],
            'stop_order': stop['order'],
            'estimated_arrival_time': stop['time'],
            'is_pickup_point': stop['order'] == 1, // First stop is pickup
            'is_drop_point': stop['order'] == busStops.length, // Last stop is drop
          }).toList();

          await _supabase
              .from('bus_stops')
              .insert(stopsToInsert);
        }
      }

      return routeResponse;
    } catch (e) {
      throw Exception('Failed to update route with stops: $e');
    }
  }

  Future<void> deleteRoute(String routeId) async {
    try {
      await _supabase
          .from('routes')
          .delete()
          .eq('id', routeId);
    } catch (e) {
      throw Exception('Failed to delete route: $e');
    }
  }

  // Driver CRUD operations
  Future<Map<String, dynamic>> createDriver({
    required String fullName,
    required String email,
    String? phoneNumber,
    required String licenseNumber,
    String? status,
  }) async {
    try {
      final response = await _supabase
          .from('drivers')
          .insert({
            'full_name': fullName,
            'email': email,
            'phone': phoneNumber,
            'driver_license': licenseNumber,
            'is_active': status == 'active' ? true : false,
          })
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('Failed to create driver: $e');
    }
  }

  Future<Map<String, dynamic>> updateDriver({
    required String driverId,
    String? fullName,
    String? email,
    String? licenseNumber,
    String? licenseExpiration,
    bool? isActive,
  }) async {
    try {
      final updateData = <String, dynamic>{};
      if (fullName != null) updateData['full_name'] = fullName;
      if (email != null) updateData['email'] = email;
      if (licenseNumber != null) updateData['driver_license'] = licenseNumber;
      if (licenseExpiration != null) updateData['license_expiry'] = licenseExpiration;
      if (isActive != null) updateData['is_active'] = isActive;

      final response = await _supabase
          .from('drivers')
          .update(updateData)
          .eq('id', driverId)
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('Failed to update driver: $e');
    }
  }

  Future<void> deleteDriver(String driverId) async {
    try {
      await _supabase
          .from('drivers')
          .delete()
          .eq('id', driverId);
    } catch (e) {
      throw Exception('Failed to delete driver: $e');
    }
  }

  // Reservation Management
  Future<List<Map<String, dynamic>>> getAllReservations() async {
    try {
      final response = await _supabase
          .from('reservations')
          .select('''
            id,
            reservation_date,
            seat_number,
            status,
            created_at,
            users!inner(
              id,
              full_name,
              email,
              student_id
            ),
            routes!inner(
              id,
              name,
              pickup_location,
              drop_location
            )
          ''')
          .order('created_at', ascending: false);

      return response.map<Map<String, dynamic>>((reservation) {
        final user = reservation['users'];
        final route = reservation['routes'];

        return {
          'id': reservation['id'],
          'reservationDate': reservation['reservation_date'],
          'seatNumber': reservation['seat_number'],
          'status': reservation['status'],
          'userName': user?['full_name'] ?? 'Unknown User',
          'userEmail': user?['email'] ?? '',
          'studentId': user?['student_id'] ?? '',
          'routeId': route?['id'] ?? '',
          'routeName': route?['name'] ?? 'Unknown Route',
          'routePickup': route?['pickup_location'] ?? '',
          'routeDrop': route?['drop_location'] ?? '',
          'createdAt': reservation['created_at'],
        };
      }).toList();
    } catch (e) {
      print('Error fetching reservations: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> updateReservationStatus({
    required String reservationId,
    required String status,
  }) async {
    try {
      final response = await _supabase
          .from('reservations')
          .update({'status': status})
          .eq('id', reservationId)
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('Failed to update reservation status: $e');
    }
  }

  Future<void> deleteReservation(String reservationId) async {
    try {
      await _supabase
          .from('reservations')
          .delete()
          .eq('id', reservationId);
    } catch (e) {
      throw Exception('Failed to delete reservation: $e');
    }
  }

  // Settings Management
  Future<Map<String, dynamic>> getSettings() async {
    try {
      return {
        'university_name': 'UniTracker University',
        'university_email': '<EMAIL>',
        'university_phone': '******-0100',
        'university_address': '123 University Ave, Campus City',
        'operating_start_time': '06:00',
        'operating_end_time': '22:00',
        'break_start_time': '12:00',
        'break_end_time': '13:00',
        'max_reservations_per_user': '5',
        'reservation_advance_days': '7',
        'cancellation_deadline_hours': '24',
        'notification_email': '<EMAIL>',
      };
    } catch (e) {
      throw Exception('Failed to get settings: $e');
    }
  }

  Future<void> updateSettings(Map<String, dynamic> settings) async {
    try {
      // For now, just return success
      // In a real app, you'd update the settings in the database
    } catch (e) {
      throw Exception('Failed to update settings: $e');
    }
  }

  // Dashboard Analytics
  Future<Map<String, dynamic>> getOnTimeRate() async {
    try {
      return {'rate': 95, 'totalTrips': 0, 'completedTrips': 0};
    } catch (e) {
      return {'rate': 95, 'totalTrips': 0, 'completedTrips': 0};
    }
  }

  Future<List<Map<String, dynamic>>> getRecentAlerts() async {
    try {
      return [
        {
          'type': 'info',
          'title': 'System Status',
          'message': 'All systems operational',
          'severity': 'info',
          'time': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
        },
      ];
    } catch (e) {
      return [];
    }
  }

  // Notifications
  Future<List<Map<String, dynamic>>> getNotifications() async {
    try {
      return [
        {
          'id': '1',
          'title': 'System Update',
          'message': 'System maintenance scheduled for tonight',
          'type': 'info',
          'isRead': false,
          'createdAt': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        },
      ];
    } catch (e) {
      return [];
    }
  }
}
