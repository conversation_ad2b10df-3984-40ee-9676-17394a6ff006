{"buildFiles": ["C:\\Users\\<USER>\\flutter_windows_3.27.4-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AndroidStudioProjects\\studioprojects\\unitracker\\android\\app\\.cxx\\Debug\\3p6d2i50\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AndroidStudioProjects\\studioprojects\\unitracker\\android\\app\\.cxx\\Debug\\3p6d2i50\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}