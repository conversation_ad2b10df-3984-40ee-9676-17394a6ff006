import 'package:flutter/material.dart';
import 'package:admin_web/theme/app_theme.dart';
import '../services/admin_data_service.dart';

class RealReservationsScreen extends StatefulWidget {
  const RealReservationsScreen({Key? key}) : super(key: key);

  @override
  State<RealReservationsScreen> createState() => _RealReservationsScreenState();
}

class _RealReservationsScreenState extends State<RealReservationsScreen> {
  List<Map<String, dynamic>> _reservations = [];
  bool _isLoading = true;
  String _search = '';
  String _statusFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadReservations();
  }

  Future<void> _loadReservations() async {
    try {
      final reservations = await AdminDataService().getAllReservations();
      setState(() {
        _reservations = reservations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load reservations: $e')),
      );
    }
  }

  List<Map<String, dynamic>> get filteredReservations {
    var filtered = _reservations;
    
    // Apply status filter
    if (_statusFilter != 'all') {
      filtered = filtered.where((r) => r['status'] == _statusFilter).toList();
    }
    
    // Apply search filter
    if (_search.isNotEmpty) {
      filtered = filtered.where((reservation) =>
          (reservation['userName']?.toString().toLowerCase().contains(_search.toLowerCase()) ?? false) ||
          (reservation['userEmail']?.toString().toLowerCase().contains(_search.toLowerCase()) ?? false) ||
          (reservation['routeName']?.toString().toLowerCase().contains(_search.toLowerCase()) ?? false)
      ).toList();
    }
    
    return filtered;
  }

  Future<void> _updateReservationStatus(String reservationId, String newStatus) async {
    try {
      await AdminDataService().updateReservationStatus(
        reservationId: reservationId,
        status: newStatus,
      );
      await _loadReservations();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Reservation status updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update reservation: $e')),
      );
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      case 'completed':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Widget _buildStatusChip(String? status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getStatusColor(status)),
      ),
      child: Text(
        status?.toUpperCase() ?? 'UNKNOWN',
        style: TextStyle(
          color: _getStatusColor(status),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showStatusUpdateDialog(Map<String, dynamic> reservation) {
    String selectedStatus = reservation['status'] ?? 'pending';
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Update Reservation Status'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('User: ${reservation['userName']}'),
              Text('Route: ${reservation['routeName']}'),
              Text('Date: ${reservation['reservationDate']}'),
              const SizedBox(height: 16),
              const Text('New Status:'),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: selectedStatus,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'pending', child: Text('Pending')),
                  DropdownMenuItem(value: 'confirmed', child: Text('Confirmed')),
                  DropdownMenuItem(value: 'cancelled', child: Text('Cancelled')),
                  DropdownMenuItem(value: 'completed', child: Text('Completed')),
                ],
                onChanged: (value) {
                  setDialogState(() {
                    selectedStatus = value ?? 'pending';
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _updateReservationStatus(reservation['id'], selectedStatus);
              },
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Reservations Management'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReservations,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Search and Filter Row
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search reservations...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) => setState(() => _search = value),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _statusFilter,
                    decoration: const InputDecoration(
                      labelText: 'Status Filter',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('All Status')),
                      DropdownMenuItem(value: 'pending', child: Text('Pending')),
                      DropdownMenuItem(value: 'confirmed', child: Text('Confirmed')),
                      DropdownMenuItem(value: 'cancelled', child: Text('Cancelled')),
                      DropdownMenuItem(value: 'completed', child: Text('Completed')),
                    ],
                    onChanged: (value) => setState(() => _statusFilter = value ?? 'all'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Statistics Row
            Row(
              children: [
                _buildStatCard('Total', _reservations.length.toString(), Colors.blue),
                const SizedBox(width: 8),
                _buildStatCard('Confirmed', 
                  _reservations.where((r) => r['status'] == 'confirmed').length.toString(), 
                  Colors.green),
                const SizedBox(width: 8),
                _buildStatCard('Pending', 
                  _reservations.where((r) => r['status'] == 'pending').length.toString(), 
                  Colors.orange),
                const SizedBox(width: 8),
                _buildStatCard('Cancelled', 
                  _reservations.where((r) => r['status'] == 'cancelled').length.toString(), 
                  Colors.red),
              ],
            ),
            const SizedBox(height: 16),
            
            // Reservations List
            Expanded(
              child: filteredReservations.isEmpty
                  ? const Center(child: Text('No reservations found'))
                  : ListView.builder(
                      itemCount: filteredReservations.length,
                      itemBuilder: (context, index) {
                        final reservation = filteredReservations[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: _getStatusColor(reservation['status']),
                              child: Text(
                                reservation['userName']?.toString().substring(0, 1).toUpperCase() ?? 'U',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            title: Text(
                              reservation['userName'] ?? 'Unknown User',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Route: ${reservation['routeName'] ?? 'Unknown'}'),
                                Text('Date: ${reservation['reservationDate'] ?? 'Unknown'}'),
                                Text('Time: ${reservation['pickupTime'] ?? 'Unknown'}'),
                                Text('Seat: ${reservation['seatNumber'] ?? 'N/A'}'),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _buildStatusChip(reservation['status']),
                                const SizedBox(width: 8),
                                IconButton(
                                  icon: const Icon(Icons.edit, color: Colors.blue),
                                  onPressed: () => _showStatusUpdateDialog(reservation),
                                ),
                              ],
                            ),
                            isThreeLine: true,
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
