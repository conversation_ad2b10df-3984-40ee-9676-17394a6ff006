import 'package:flutter/material.dart';
import 'package:admin_web/theme/app_theme.dart';
import '../services/admin_data_service.dart';

class SimpleBusesScreen extends StatefulWidget {
  const SimpleBusesScreen({Key? key}) : super(key: key);

  @override
  State<SimpleBusesScreen> createState() => _SimpleBusesScreenState();
}

class _SimpleBusesScreenState extends State<SimpleBusesScreen> {
  List<Map<String, dynamic>> _buses = [];
  List<Map<String, dynamic>> _drivers = [];
  bool _isLoading = true;
  String _search = '';

  @override
  void initState() {
    super.initState();
    _loadBuses();
    _loadDrivers();
  }

  Future<void> _loadBuses() async {
    try {
      final buses = await AdminDataService().getAllBuses();
      setState(() {
        _buses = buses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load buses: $e')),
      );
    }
  }

  Future<void> _loadDrivers() async {
    try {
      final drivers = await AdminDataService().getAllDrivers();
      setState(() {
        _drivers = drivers;
      });
    } catch (e) {
      print('Failed to load drivers: $e');
    }
  }

  List<Map<String, dynamic>> get filteredBuses {
    if (_search.isEmpty) return _buses;
    return _buses.where((bus) =>
        (bus['busNumber']?.toString().toLowerCase().contains(_search.toLowerCase()) ?? false) ||
        (bus['id']?.toString().toLowerCase().contains(_search.toLowerCase()) ?? false)
    ).toList();
  }

  Future<void> _deleteBus(String busId) async {
    try {
      await AdminDataService().deleteBus(busId);
      await _loadBuses();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Bus deleted successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete bus: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Buses Management'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Search Bar
            TextField(
              decoration: const InputDecoration(
                hintText: 'Search buses...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) => setState(() => _search = value),
            ),
            const SizedBox(height: 16),
            
            // Buses List
            Expanded(
              child: filteredBuses.isEmpty
                  ? const Center(child: Text('No buses found'))
                  : ListView.builder(
                      itemCount: filteredBuses.length,
                      itemBuilder: (context, index) {
                        final bus = filteredBuses[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: AppTheme.primaryColor,
                              child: Text(
                                bus['busNumber']?.toString().substring(0, 1) ?? 'B',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            title: Text(
                              'Bus ${bus['busNumber'] ?? 'Unknown'}',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Capacity: ${bus['capacity'] ?? 'N/A'}'),
                                Text('Status: ${bus['status'] ?? 'Unknown'}'),
                                Text('Driver: ${_getDriverName(bus['driverId']) ?? 'Unassigned'}'),
                                Text('Route: ${bus['routeName'] ?? 'No Route'}'),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.person_add, color: Colors.green),
                                  onPressed: () => _showAssignDriverDialog(bus),
                                  tooltip: 'Assign Driver',
                                ),
                                IconButton(
                                  icon: const Icon(Icons.info, color: Colors.blue),
                                  onPressed: () => _showBusDetails(bus),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete, color: Colors.red),
                                  onPressed: () => _confirmDelete(bus),
                                ),
                              ],
                            ),
                            isThreeLine: true,
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddBusDialog,
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  String? _getDriverName(String? driverId) {
    if (driverId == null) return null;
    final driver = _drivers.firstWhere(
      (d) => d['id'] == driverId,
      orElse: () => {},
    );
    return driver['fullName'];
  }

  void _showAssignDriverDialog(Map<String, dynamic> bus) {
    String? selectedDriverId = bus['driverId'];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Assign Driver to ${bus['busNumber']}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Select a driver:'),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedDriverId,
                decoration: const InputDecoration(
                  labelText: 'Driver',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('Unassigned'),
                  ),
                  ..._drivers.map((driver) => DropdownMenuItem<String>(
                    value: driver['id'],
                    child: Text(driver['fullName'] ?? 'Unknown'),
                  )).toList(),
                ],
                onChanged: (value) {
                  setDialogState(() {
                    selectedDriverId = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  await AdminDataService().updateBus(
                    busId: bus['id'],
                    driverId: selectedDriverId,
                  );
                  Navigator.pop(context);
                  await _loadBuses();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Driver assigned successfully')),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to assign driver: $e')),
                  );
                }
              },
              child: const Text('Assign'),
            ),
          ],
        ),
      ),
    );
  }

  void _showBusDetails(Map<String, dynamic> bus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Bus Details - ${bus['busNumber']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${bus['id']}'),
            Text('Bus Number: ${bus['busNumber']}'),
            Text('Capacity: ${bus['capacity']}'),
            Text('Status: ${bus['status']}'),
            Text('Driver: ${_getDriverName(bus['driverId']) ?? 'Unassigned'}'),
            Text('Route: ${bus['routeName'] ?? 'No Route'}'),
            Text('Created: ${bus['createdAt']?.toString().split('T')[0] ?? 'N/A'}'),
            Text('Updated: ${bus['updatedAt']?.toString().split('T')[0] ?? 'N/A'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _confirmDelete(Map<String, dynamic> bus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete bus ${bus['busNumber']}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteBus(bus['id']);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showAddBusDialog() {
    final busNumberController = TextEditingController();
    final plateNumberController = TextEditingController();
    final capacityController = TextEditingController(text: '50');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Bus'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: busNumberController,
              decoration: const InputDecoration(
                labelText: 'Bus Number',
                hintText: 'e.g., BUS-001',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: plateNumberController,
              decoration: const InputDecoration(
                labelText: 'Plate Number',
                hintText: 'e.g., ABC-1234',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: capacityController,
              decoration: const InputDecoration(
                labelText: 'Capacity',
                hintText: 'e.g., 50',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (busNumberController.text.isNotEmpty && plateNumberController.text.isNotEmpty) {
                try {
                  await AdminDataService().createBus(
                    busNumber: busNumberController.text,
                    plateNumber: plateNumberController.text,
                    capacity: int.tryParse(capacityController.text) ?? 50,
                  );
                  Navigator.pop(context);
                  await _loadBuses();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Bus added successfully')),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to add bus: $e')),
                  );
                }
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}
