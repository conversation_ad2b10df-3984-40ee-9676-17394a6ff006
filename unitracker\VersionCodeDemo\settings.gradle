pluginManagement {
    includeBuild("build-logic")

    apply from: "$settingsDir/gradle/repositories.gradle"
    settings.ext.repos.google(repositories)
    settings.ext.repos.gradlePluginPortal(repositories)

    plugins {
        id 'com.android.library' version "$androidGradlePluginVersion"
        id 'com.android.application' version "$androidGradlePluginVersion"
        id 'com.android.dynamic-feature' version "$androidGradlePluginVersion"
        id 'com.android.settings' version "$androidGradlePluginVersion"
        id 'org.jetbrains.kotlin.android' version "$kotlinVersion"
        id 'org.jetbrains.kotlin.kapt' version "$kotlinVersion"
        id 'org.gradle.test-retry' version "$gradleTestRetryVersion"
    }
}

plugins {
    id 'com.android.settings'
}

android {
    def _minSdk = settings.getProperty("minSdk").toInteger()
    def _compileSdkPreview = settings.getProperty("compileSdkPreview")
    def _compileSdkExtension = settings.getProperty("compileSdkExtension")
    def _buildToolsVersion = settings.getProperty("buildToolsVersion")

    compileSdk settings.getProperty("compileSdk").toInteger()

    if (!_compileSdkExtension.isEmpty()) {
        compileSdkExtension _compileSdkExtension.toInteger()
    }

    if (!_compileSdkPreview.isEmpty()) {
        compileSdkPreview _compileSdkPreview
    }

    minSdk _minSdk
    ndkVersion "$NDK_VERSION"
    buildToolsVersion _buildToolsVersion
    execution {
        defaultProfile "minimal"
        profiles {
            minimal {
                r8 {
                    runInSeparateProcess true
                    jvmOptions = ["-Xmx2g", "-XX:+UseParallelGC"]
                }
            }
        }
    }
}

dependencyResolutionManagement {
    settings.ext.repos.google(repositories)
    settings.ext.repos.mavenCentral(repositories)
}

apply from: "gradle/version_catalogs.gradle"

include ':app', ':dynamicfeature1', ":shared"
include ":lib:hostconfig"
rootProject.name='My Application'
