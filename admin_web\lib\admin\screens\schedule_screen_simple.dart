import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:admin_web/theme/app_theme.dart';
import '../services/admin_data_service.dart';

class SimpleScheduleScreen extends StatefulWidget {
  const SimpleScheduleScreen({Key? key}) : super(key: key);

  @override
  State<SimpleScheduleScreen> createState() => _SimpleScheduleScreenState();
}

class _SimpleScheduleScreenState extends State<SimpleScheduleScreen> {
  List<Map<String, dynamic>> _routes = [];
  List<Map<String, dynamic>> _buses = [];
  bool _isLoading = true;
  DateTime _selectedWeek = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final routes = await AdminDataService().getAllRoutes();
      final buses = await AdminDataService().getAllBuses();
      
      setState(() {
        _routes = routes;
        _buses = buses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load data: $e')),
      );
    }
  }

  DateTime _getStartOfWeek(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  void _changeWeek(int delta) {
    setState(() {
      _selectedWeek = _selectedWeek.add(Duration(days: 7 * delta));
    });
  }

  List<String> get _weekDays {
    final startOfWeek = _getStartOfWeek(_selectedWeek);
    return List.generate(7, (index) {
      final day = startOfWeek.add(Duration(days: index));
      return DateFormat('EEE\nMMM d').format(day);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final weekStart = DateFormat('MMM d').format(_getStartOfWeek(_selectedWeek));
    final weekEnd = DateFormat('MMM d, yyyy').format(_getStartOfWeek(_selectedWeek).add(const Duration(days: 6)));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Schedule'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Week Navigation
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.chevron_left),
                      onPressed: () => _changeWeek(-1),
                    ),
                    Text(
                      '$weekStart - $weekEnd',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.chevron_right),
                      onPressed: () => _changeWeek(1),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Schedule Grid
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Container(
                  width: MediaQuery.of(context).size.width > 800 
                      ? MediaQuery.of(context).size.width - 32
                      : 800,
                  child: Column(
                    children: [
                      // Day Headers
                      Container(
                        height: 60,
                        child: Row(
                          children: [
                            Container(
                              width: 100,
                              child: const Text(
                                'Time',
                                style: TextStyle(fontWeight: FontWeight.bold),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            ..._weekDays.map((day) => Expanded(
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryColor.withOpacity(0.1),
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                child: Text(
                                  day,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            )).toList(),
                          ],
                        ),
                      ),

                      // Schedule Content
                      Expanded(
                        child: ListView.builder(
                          itemCount: _routes.length,
                          itemBuilder: (context, index) {
                            final route = _routes[index];
                            return Container(
                              height: 80,
                              child: Row(
                                children: [
                                  Container(
                                    width: 100,
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey.shade300),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          route['startTime'] ?? '08:00',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          route['endTime'] ?? '18:00',
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  ...List.generate(7, (dayIndex) {
                                    // Find buses assigned to this route for this day
                                    final assignedBuses = _buses.where((bus) => 
                                        bus['routeName'] == route['name'] && 
                                        bus['status'] == 'active'
                                    ).toList();

                                    return Expanded(
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          border: Border.all(color: Colors.grey.shade300),
                                        ),
                                        child: assignedBuses.isNotEmpty
                                            ? Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    padding: const EdgeInsets.symmetric(
                                                        horizontal: 6, vertical: 2),
                                                    decoration: BoxDecoration(
                                                      color: AppTheme.primaryColor,
                                                      borderRadius: BorderRadius.circular(4),
                                                    ),
                                                    child: Text(
                                                      route['name'] ?? 'Route',
                                                      style: const TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 10,
                                                        fontWeight: FontWeight.bold,
                                                      ),
                                                      maxLines: 1,
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 2),
                                                  Text(
                                                    assignedBuses.first['busNumber'] ?? 'Bus',
                                                    style: TextStyle(
                                                      fontSize: 9,
                                                      color: Colors.grey.shade600,
                                                    ),
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ],
                                              )
                                            : Container(),
                                      ),
                                    );
                                  }),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Summary Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Schedule Summary',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _SummaryItem(
                          title: 'Active Routes',
                          value: _routes.where((r) => r['status'] == 'active').length.toString(),
                          icon: Icons.route,
                        ),
                        _SummaryItem(
                          title: 'Active Buses',
                          value: _buses.where((b) => b['status'] == 'active').length.toString(),
                          icon: Icons.directions_bus,
                        ),
                        _SummaryItem(
                          title: 'Total Capacity',
                          value: _buses.fold<int>(0, (sum, bus) => sum + (bus['capacity'] as int? ?? 0)).toString(),
                          icon: Icons.people,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddScheduleDialog,
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _showAddScheduleDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Schedule'),
        content: const Text('Schedule management functionality will be available soon.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class _SummaryItem extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;

  const _SummaryItem({
    required this.title,
    required this.value,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.primaryColor, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}
