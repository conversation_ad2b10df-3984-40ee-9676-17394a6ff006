import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../theme/app_theme.dart';
import '../../utils/responsive_utils.dart';
import '../../services/driver_service.dart';
import '../../services/auth_service.dart';
import '../../services/notification_service.dart';
import '../driver/driver_notifications_screen.dart';

class DriverRouteScreen extends StatefulWidget {
  const DriverRouteScreen({super.key});

  @override
  State<DriverRouteScreen> createState() => _DriverRouteScreenState();
}

class _DriverRouteScreenState extends State<DriverRouteScreen> {
  @override
  void initState() {
    super.initState();
    _loadDriverData();
  }

  Future<void> _loadDriverData() async {
    try {
      final authService = context.read<AuthService>();
      final currentUser = authService.currentUser;

      debugPrint('🚗 DriverRouteScreen: Current user: ${currentUser?.name}, Role: ${currentUser?.role}, ID: ${currentUser?.id}');

      if (currentUser != null && currentUser.role == 'driver') {
        debugPrint('🚗 DriverRouteScreen: Loading driver data for ID: ${currentUser.id}');
        await DriverService.instance.loadDriverData(currentUser.id);
      } else {
        debugPrint('⚠️ DriverRouteScreen: No driver user found or wrong role');
      }
    } catch (e) {
      debugPrint('❌ DriverRouteScreen: Error loading driver data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: DriverService.instance,
      builder: (context, child) {
        final driverService = DriverService.instance;
        
        if (driverService.isLoading) {
          return Scaffold(
            backgroundColor: AppTheme.backgroundColor,
            appBar: AppBar(
              backgroundColor: AppTheme.primaryColor,
              elevation: 0,
              automaticallyImplyLeading: false,
              title: const Text(
                'Driver Dashboard',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
              ),
            ),
            body: const Center(child: CircularProgressIndicator()),
          );
        }

        if (driverService.error != null) {
          return Scaffold(
            backgroundColor: AppTheme.backgroundColor,
            appBar: AppBar(
              backgroundColor: AppTheme.primaryColor,
              elevation: 0,
              automaticallyImplyLeading: false,
              title: const Text(
                'Driver Dashboard',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
              ),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: ${driverService.error}'),
                  ElevatedButton(
                    onPressed: _loadDriverData,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        if (driverService.assignedRoute == null) {
          return Scaffold(
            backgroundColor: AppTheme.backgroundColor,
            appBar: AppBar(
              backgroundColor: AppTheme.primaryColor,
              elevation: 0,
              automaticallyImplyLeading: false,
              title: const Text(
                'Driver Dashboard',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
              ),
            ),
            body: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.route_outlined, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'No Route Assigned',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Please contact your supervisor for route assignment.',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          );
        }

        if (!driverService.hasScheduleToday) {
          return Scaffold(
            backgroundColor: AppTheme.backgroundColor,
            appBar: AppBar(
              backgroundColor: AppTheme.primaryColor,
              elevation: 0,
              automaticallyImplyLeading: false,
              title: const Text(
                'Driver Dashboard',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
              ),
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.calendar_today, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  const Text(
                    'No Schedule Today',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your route "${driverService.assignedRoute!.name}" is not scheduled for ${_getDayName(DateTime.now().weekday)}.',
                    style: const TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Scheduled days: ${_getScheduledDays(driverService.todaySchedule)}',
                    style: const TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return _buildDriverDashboard(context, driverService);
      },
    );
  }

  Widget _buildDriverDashboard(BuildContext context, DriverService driverService) {
    SizeConfig.init(context);
    final isTablet = SizeConfig.screenWidth > 600;
    final padding = getProportionateScreenWidth(isTablet ? 24 : 16);

    final route = driverService.assignedRoute!;
    final stops = driverService.routeStops;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Text(
          'Driver Dashboard',
          style: Theme.of(context).textTheme.displayMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          Consumer<NotificationService>(
            builder: (context, notificationService, _) => Stack(
              children: [
                IconButton(
                  icon: const Icon(Icons.notifications_outlined, color: Colors.white),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DriverNotificationsScreen(),
                      ),
                    );
                  },
                ),
                if (notificationService.unreadCount > 0)
                  Positioned(
                    right: 8,
                    top: 8,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: AppTheme.errorColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                      child: Text(
                        '${notificationService.unreadCount}',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Route Info Card
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppTheme.secondaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.directions_bus,
                        color: AppTheme.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            route.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2B2087),
                            ),
                          ),
                          Text(
                            '${route.pickup} → ${route.drop}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppTheme.successColor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Text(
                        'Active',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(child: _buildInfoItem('Schedule', driverService.scheduledDays)),
                    Expanded(child: _buildInfoItem('Start Time', route.startTime)),
                    Expanded(child: _buildInfoItem('End Time', route.endTime)),
                  ],
                ),
              ],
            ),
          ),

          // Route Progress Section
          if (driverService.isTripInProgress) ...[
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Route Progress',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2B2087),
                        ),
                      ),
                      Text(
                        '${_calculateProgress(driverService)}%',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Progress Bar
                  Container(
                    height: 6,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: _calculateProgress(driverService) / 100,
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // Current and Next Stop Preview
                  Row(
                    children: [
                      // Current Stop
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.location_on, size: 16, color: AppTheme.primaryColor),
                                const SizedBox(width: 4),
                                const Text(
                                  'Current',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _getCurrentStopName(driverService),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Next Stop
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.flag, size: 16, color: Colors.orange),
                                const SizedBox(width: 4),
                                const Text(
                                  'Next',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _getNextStopName(driverService),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // On Time Status
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'On Time',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Current Stop Card (if trip is in progress)
          if (driverService.isTripInProgress) ...[
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.location_on, color: AppTheme.primaryColor, size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'Current Stop',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF2B2087),
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: AppTheme.secondaryColor,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Scheduled: ${route.startTime}',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    driverService.routeStops.isNotEmpty && driverService.currentStopIndex < driverService.routeStops.length
                      ? driverService.routeStops[driverService.currentStopIndex].name ?? 'Current Stop'
                      : 'Current Stop',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildTripActionButton(context, driverService),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ] else ...[
            // Trip Action Button when not in progress
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildTripActionButton(context, driverService),
            ),
            const SizedBox(height: 16),
          ],

          // Route Stops
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: AppTheme.secondaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.format_list_bulleted,
                            color: AppTheme.primaryColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Route Stops',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2B2087),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: stops.length,
                      itemBuilder: (context, index) => _buildStopItem(context, driverService, stops[index], index),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildStopItem(BuildContext context, DriverService driverService, dynamic stop, int index) {
    try {
      final stopCheckIns = driverService.stopCheckIns;
      final isCheckedIn = index < stopCheckIns.length && stopCheckIns[index];
      final isCurrentStop = driverService.isTripInProgress && index == driverService.currentStopIndex;
      final isPastStop = index < driverService.currentStopIndex;
      final isUpcoming = index > driverService.currentStopIndex;

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          color: isUpcoming ? Colors.grey.shade50.withOpacity(0.5) : Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.shade200,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isCheckedIn
                  ? AppTheme.successColor
                  : isCurrentStop
                    ? AppTheme.primaryColor
                    : isUpcoming
                      ? Colors.grey.shade300.withOpacity(0.5)
                      : Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: isCheckedIn
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 20,
                    )
                  : isCurrentStop
                    ? const Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 20,
                      )
                    : Text(
                        '${index + 1}',
                        style: TextStyle(
                          color: isUpcoming
                            ? Colors.grey.shade400
                            : Colors.grey.shade600,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          stop.name ?? 'Stop ${index + 1}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isCheckedIn
                              ? AppTheme.successColor
                              : isCurrentStop
                                ? AppTheme.primaryColor
                                : isUpcoming
                                  ? Colors.grey.shade400
                                  : Colors.black87,
                          ),
                        ),
                      ),
                      if (isCurrentStop)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'Current',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      if (isCheckedIn)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppTheme.successColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'Completed',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (stop.arrivalTime != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 14,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            stop.arrivalTime!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('Error building stop item: $e');
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.red.shade100,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.red, width: 1.5),
              ),
              child: const Icon(Icons.error, color: Colors.red, size: 18),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Text(
                'Error loading stop',
                style: TextStyle(fontSize: 16, color: Colors.red),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildTripActionButton(BuildContext context, DriverService driverService) {
    try {
      // Check if trip is completed today
      if (driverService.isTripCompletedToday) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: AppTheme.successColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(color: AppTheme.successColor.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: AppTheme.successColor, size: 20),
              const SizedBox(width: 8),
              Text(
                'Trip Completed',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.successColor,
                ),
              ),
            ],
          ),
        );
      }

      if (driverService.canStartTrip) {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _handleStartTrip(context, driverService),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 0,
            ),
            child: const Text(
              'Start Trip',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      }

      if (driverService.isTripInProgress) {
        final currentIndex = driverService.currentStopIndex;
        final stops = driverService.routeStops;

        if (currentIndex < stops.length) {
          final isLastStop = currentIndex >= stops.length - 1;
          final currentStop = stops[currentIndex];

          return SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _handleCheckIn(context, driverService, currentIndex, isLastStop),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                elevation: 0,
              ),
              child: Text(
                isLastStop
                  ? 'Complete Trip'
                  : 'Mark as Arrived',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          );
        }
      }

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(25),
        ),
        child: const Text(
          'No Trip Available',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
      );
    } catch (e) {
      debugPrint('Error building trip action button: $e');
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.red.shade100,
          borderRadius: BorderRadius.circular(25),
        ),
        child: const Text(
          'Error',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
      );
    }
  }

  Future<void> _handleStartTrip(BuildContext context, DriverService driverService) async {
    try {
      await driverService.startTrip();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Trip started! Check in at each stop.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting trip: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleCheckIn(BuildContext context, DriverService driverService, int stopIndex, bool isLastStop) async {
    try {
      await driverService.checkInAtStop(stopIndex);
      if (mounted) {
        final stopName = driverService.routeStops[stopIndex].name;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isLastStop
                ? 'Trip completed successfully!'
                : 'Checked in at $stopName'
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  int _calculateProgress(DriverService driverService) {
    if (!driverService.isTripInProgress || driverService.routeStops.isEmpty) {
      return 0;
    }

    final totalStops = driverService.routeStops.length;
    final completedStops = driverService.currentStopIndex;

    if (completedStops >= totalStops) {
      return 100;
    }

    return ((completedStops / totalStops) * 100).round();
  }

  String _getCurrentStopName(DriverService driverService) {
    if (!driverService.isTripInProgress ||
        driverService.currentStopIndex >= driverService.routeStops.length) {
      return 'No current stop';
    }

    final currentStop = driverService.routeStops[driverService.currentStopIndex];
    return currentStop.name ?? 'Stop ${driverService.currentStopIndex + 1}';
  }

  String _getNextStopName(DriverService driverService) {
    final nextIndex = driverService.currentStopIndex + 1;

    if (!driverService.isTripInProgress ||
        nextIndex >= driverService.routeStops.length) {
      return 'Trip End';
    }

    final nextStop = driverService.routeStops[nextIndex];
    return nextStop.name ?? 'Stop ${nextIndex + 1}';
  }



  void _showCompleteTrip(BuildContext context, DriverService driverService) {
    final passengerCountController = TextEditingController();
    final notesController = TextEditingController();
    bool onTime = true;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Trip'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: passengerCountController,
              decoration: const InputDecoration(
                labelText: 'Passenger Count',
                hintText: 'Enter number of passengers',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            StatefulBuilder(
              builder: (context, setState) => CheckboxListTile(
                title: const Text('Trip completed on time'),
                value: onTime,
                onChanged: (value) => setState(() => onTime = value ?? true),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Any additional notes...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final passengerCount = int.tryParse(passengerCountController.text) ?? 0;
                
                await driverService.completeTrip(
                  passengerCount: passengerCount,
                  distanceKm: 45.5,
                  onTime: onTime,
                  notes: notesController.text.isEmpty ? null : notesController.text,
                );

                Navigator.pop(context);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Trip completed successfully!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                Navigator.pop(context);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error completing trip: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Complete'),
          ),
        ],
      ),
    );
  }

  String _getDayName(int dayOfWeek) {
    const days = ['', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[dayOfWeek];
  }

  String _getScheduledDays(Map<String, dynamic>? schedule) {
    try {
      if (schedule == null) return 'No schedule available';

      final daysOfWeek = schedule['days_of_week'] as List<dynamic>?;
      if (daysOfWeek == null || daysOfWeek.isEmpty) return 'No days scheduled';

      const dayNames = ['', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      final scheduledDayNames = daysOfWeek
          .where((day) => day is int && day >= 1 && day <= 7)
          .map((day) => dayNames[day as int])
          .toList();

      return scheduledDayNames.isNotEmpty ? scheduledDayNames.join(', ') : 'No valid days';
    } catch (e) {
      debugPrint('Error getting scheduled days: $e');
      return 'Schedule unavailable';
    }
  }
}
