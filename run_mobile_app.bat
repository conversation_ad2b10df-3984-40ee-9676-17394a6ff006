@echo off
echo Starting UniTracker Mobile App...

REM Navigate to the unitracker directory
cd /d "%~dp0unitracker"

REM Check if we're in the right directory
if not exist "pubspec.yaml" (
    echo Error: pubspec.yaml not found. Make sure you're in the correct directory.
    pause
    exit /b 1
)

echo Cleaning Flutter project...
flutter clean

echo Getting dependencies...
flutter pub get

echo Starting Flutter app for web...
flutter run -d chrome --web-port=3002

pause
