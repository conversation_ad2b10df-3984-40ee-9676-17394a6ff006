import 'package:flutter/material.dart';
import 'package:admin_web/theme/app_theme.dart';
import 'package:admin_web/widgets/animated_widgets.dart';
import '../services/admin_data_service.dart';
import 'dart:ui';

class ModernScheduleScreen extends StatefulWidget {
  const ModernScheduleScreen({Key? key}) : super(key: key);

  @override
  State<ModernScheduleScreen> createState() => _ModernScheduleScreenState();
}

class _ModernScheduleScreenState extends State<ModernScheduleScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  List<Map<String, dynamic>> _schedules = [];
  List<Map<String, dynamic>> _routes = [];
  List<Map<String, dynamic>> _buses = [];
  List<Map<String, dynamic>> _drivers = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _dayFilter = 'all';

  final List<String> _weekDays = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final results = await Future.wait([
        AdminDataService().getAllSchedules(),
        AdminDataService().getAllRoutes(),
        AdminDataService().getAllBuses(),
        AdminDataService().getAllDrivers(),
      ]);

      setState(() {
        _schedules = results[0] as List<Map<String, dynamic>>;
        _routes = results[1] as List<Map<String, dynamic>>;
        _buses = results[2] as List<Map<String, dynamic>>;
        _drivers = results[3] as List<Map<String, dynamic>>;
        _isLoading = false;
      });

      // Debug prints
      print('Loaded ${_routes.length} routes: ${_routes.map((r) => r['name']).toList()}');
      print('Loaded ${_buses.length} buses: ${_buses.map((b) => b['busNumber']).toList()}');
      print('Loaded ${_drivers.length} drivers: ${_drivers.map((d) => d['fullName']).toList()}');

      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> get filteredSchedules {
    var filtered = _schedules;

    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((schedule) {
        final routeName = (schedule['routeName'] ?? _getRouteName(schedule['routeId']) ?? '').toLowerCase();
        final busNumber = (schedule['busNumber'] ?? _getBusNumber(schedule['busId']) ?? '').toLowerCase();
        final time = schedule['departureTime']?.toString().toLowerCase() ?? '';
        final query = _searchQuery.toLowerCase();

        return routeName.contains(query) || busNumber.contains(query) || time.contains(query);
      }).toList();
    }

    if (_dayFilter != 'all') {
      filtered = filtered.where((schedule) {
        final days = schedule['days'] as List? ?? [];
        return days.contains(_dayFilter);
      }).toList();
    }

    return filtered;
  }

  void _showAddScheduleDialog() {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => _AddScheduleDialog(
        routes: _routes,
        buses: _buses,
        drivers: _drivers,
        onScheduleAdded: () {
          _loadData();
        },
      ),
    );
  }

  void _showEditScheduleDialog(Map<String, dynamic> schedule) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => _AddScheduleDialog(
        schedule: schedule,
        routes: _routes,
        buses: _buses,
        drivers: _drivers,
        onScheduleAdded: () {
          _loadData();
        },
      ),
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> schedule) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.3),
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('Delete Schedule'),
        content: Text('Are you sure you want to delete this schedule for ${schedule['routeName'] ?? 'Unknown Route'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteSchedule(schedule['id']);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEF4444),
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSchedule(String scheduleId) async {
    try {
      await AdminDataService().deleteSchedule(scheduleId);
      await _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Schedule deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete schedule: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFF8FAFC),
            const Color(0xFFE2E8F0).withOpacity(0.3),
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildSearchAndFilters(),
            const SizedBox(height: 24),
            _buildStatsCards(),
            const SizedBox(height: 24),
            Expanded(child: _buildSchedulesList()),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFF8FAFC),
            const Color(0xFFE2E8F0).withOpacity(0.3),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedWidgets.shimmer(
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Loading Schedules...',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF64748B),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF06B6D4),
            const Color(0xFF06B6D4).withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF06B6D4).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Schedule Management',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Manage bus schedules, timetables, and route assignments.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(40),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.schedule_rounded,
              size: 40,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFE2E8F0)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search schedules by route, bus, or time...',
                hintStyle: const TextStyle(color: Color(0xFF94A3B8)),
                prefixIcon: const Icon(Icons.search_rounded, color: Color(0xFF94A3B8)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              onChanged: (value) => setState(() => _searchQuery = value),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFFE2E8F0)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _dayFilter,
              items: [
                const DropdownMenuItem(value: 'all', child: Text('All Days')),
                ..._weekDays.map((day) => DropdownMenuItem(value: day, child: Text(day))),
              ],
              onChanged: (value) => setState(() => _dayFilter = value!),
            ),
          ),
        ),
        const SizedBox(width: 16),
        AnimatedWidgets.scaleButton(
          onTap: _showAddScheduleDialog,
          child: Container(
            height: 56,
            padding: const EdgeInsets.symmetric(horizontal: 24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [const Color(0xFF06B6D4), const Color(0xFF06B6D4).withOpacity(0.8)],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF06B6D4).withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.add_rounded, color: Colors.white),
                const SizedBox(width: 8),
                const Text(
                  'Add Schedule',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCards() {
    final totalSchedules = _schedules.length;
    final todaySchedules = _schedules.where((schedule) {
      final days = schedule['days'] as List? ?? [];
      final today = _weekDays[DateTime.now().weekday - 1];
      return days.contains(today);
    }).length;
    final activeRoutes = _routes.where((route) => route['status'] == 'active').length;
    final activeBuses = _buses.where((bus) => bus['status'] == 'active').length;

    final stats = [
      {
        'title': 'Total Schedules',
        'value': totalSchedules.toString(),
        'icon': Icons.schedule_rounded,
        'color': const Color(0xFF3B82F6),
        'description': 'All schedules',
      },
      {
        'title': 'Today',
        'value': todaySchedules.toString(),
        'icon': Icons.today_rounded,
        'color': const Color(0xFF10B981),
        'description': 'Running today',
      },
      {
        'title': 'Active Routes',
        'value': activeRoutes.toString(),
        'icon': Icons.route_rounded,
        'color': const Color(0xFFF59E0B),
        'description': 'In service',
      },
      {
        'title': 'Active Buses',
        'value': activeBuses.toString(),
        'icon': Icons.directions_bus_rounded,
        'color': const Color(0xFF8B5CF6),
        'description': 'Available',
      },
    ];

    return Row(
      children: stats.map((stat) => Expanded(
        child: Container(
          margin: const EdgeInsets.only(right: 16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: (stat['color'] as Color).withOpacity(0.1)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (stat['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      stat['icon'] as IconData,
                      color: stat['color'] as Color,
                      size: 20,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    stat['value'] as String,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF0F172A),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                stat['title'] as String,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF0F172A),
                ),
              ),
              Text(
                stat['description'] as String,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF64748B),
                ),
              ),
            ],
          ),
        ),
      )).toList(),
    );
  }

  Widget _buildSchedulesList() {
    if (filteredSchedules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: const Color(0xFF64748B).withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                Icons.schedule_outlined,
                size: 48,
                color: const Color(0xFF64748B),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'No schedules found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF64748B),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Add your first schedule to get started',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF94A3B8),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredSchedules.length,
      itemBuilder: (context, index) {
        final schedule = filteredSchedules[index];
        return _buildScheduleCard(schedule, index);
      },
    );
  }

  Widget _buildScheduleCard(Map<String, dynamic> schedule, int index) {
    final routeName = schedule['routeName'] ?? _getRouteName(schedule['routeId']) ?? 'Unknown Route';
    final busNumber = schedule['busNumber'] ?? _getBusNumber(schedule['busId']) ?? 'Unknown Bus';
    final days = schedule['days'] as List? ?? [];
    final isToday = days.contains(_weekDays[DateTime.now().weekday - 1]);

    return AnimatedWidgets.modernCard(
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isToday
                    ? const Color(0xFF10B981).withOpacity(0.1)
                    : const Color(0xFF06B6D4).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.schedule_rounded,
                color: isToday ? const Color(0xFF10B981) : const Color(0xFF06B6D4),
                size: 24,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        routeName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0F172A),
                        ),
                      ),
                      const SizedBox(width: 12),
                      if (isToday)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: const Color(0xFF10B981).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: const Color(0xFF10B981).withOpacity(0.3)),
                          ),
                          child: const Text(
                            'TODAY',
                            style: TextStyle(
                              color: Color(0xFF10B981),
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.directions_bus_rounded,
                        color: const Color(0xFF64748B),
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Bus $busNumber',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF64748B),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        Icons.access_time_rounded,
                        color: const Color(0xFF64748B),
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        schedule['departureTime'] ?? 'N/A',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF64748B),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 4,
                    children: days.map<Widget>((day) => Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFF94A3B8).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        day.toString().substring(0, 3),
                        style: const TextStyle(
                          fontSize: 10,
                          color: Color(0xFF94A3B8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )).toList(),
                  ),
                ],
              ),
            ),
            Column(
              children: [
                AnimatedWidgets.scaleButton(
                  onTap: () => _showEditScheduleDialog(schedule),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF3B82F6).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.edit_rounded,
                      color: const Color(0xFF3B82F6),
                      size: 16,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                AnimatedWidgets.scaleButton(
                  onTap: () => _showDeleteConfirmation(schedule),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFFEF4444).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.delete_rounded,
                      color: const Color(0xFFEF4444),
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String? _getRouteName(String? routeId) {
    if (routeId == null) return null;
    try {
      final route = _routes.firstWhere((r) => r['id'] == routeId);
      return route['name'];
    } catch (e) {
      return null;
    }
  }

  String? _getBusNumber(String? busId) {
    if (busId == null) return null;
    try {
      final bus = _buses.firstWhere((b) => b['id'] == busId);
      return bus['busNumber'];
    } catch (e) {
      return null;
    }
  }
}

// Add Schedule Dialog
class _AddScheduleDialog extends StatefulWidget {
  final Map<String, dynamic>? schedule;
  final List<Map<String, dynamic>> routes;
  final List<Map<String, dynamic>> buses;
  final List<Map<String, dynamic>> drivers;
  final VoidCallback onScheduleAdded;

  const _AddScheduleDialog({
    this.schedule,
    required this.routes,
    required this.buses,
    required this.drivers,
    required this.onScheduleAdded,
  });

  @override
  State<_AddScheduleDialog> createState() => _AddScheduleDialogState();
}

class _AddScheduleDialogState extends State<_AddScheduleDialog> {
  final _formKey = GlobalKey<FormState>();
  final _departureTimeController = TextEditingController();
  final _arrivalTimeController = TextEditingController();
  String? _selectedRouteId;
  String? _selectedBusId;
  String? _selectedDriverId;
  List<String> _selectedDays = [];
  bool _isLoading = false;

  final List<String> _weekDays = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.schedule != null) {
      // Initialize with existing schedule data
      _selectedRouteId = widget.schedule!['routeId'];
      _selectedBusId = widget.schedule!['busId'];
      _departureTimeController.text = widget.schedule!['departureTime'] ?? '';
      _arrivalTimeController.text = widget.schedule!['arrivalTime'] ?? '';
      _selectedDays = List<String>.from(widget.schedule!['days'] ?? []);
    }
  }

  @override
  void dispose() {
    _departureTimeController.dispose();
    _arrivalTimeController.dispose();
    super.dispose();
  }

  void _autoFillTimes(String? routeId) {
    if (routeId == null) return;

    try {
      final route = widget.routes.firstWhere((r) => r['id'] == routeId);
      final startTime = route['startTime']?.toString() ?? '';
      final endTime = route['endTime']?.toString() ?? '';

      if (startTime.isNotEmpty) {
        _departureTimeController.text = _formatTimeForInput(startTime);
      }
      if (endTime.isNotEmpty) {
        _arrivalTimeController.text = _formatTimeForInput(endTime);
      }
    } catch (e) {
      // Route not found, ignore
    }
  }

  String _formatTimeForInput(String time) {
    // Convert time formats like "08:00:00" to "08:00"
    if (time.contains(':')) {
      final parts = time.split(':');
      if (parts.length >= 2) {
        return '${parts[0]}:${parts[1]}';
      }
    }
    return time;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 600,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF06B6D4).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.schedule_rounded,
                        color: const Color(0xFF06B6D4),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      widget.schedule == null ? 'Add New Schedule' : 'Edit Schedule',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF0F172A),
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close_rounded),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedRouteId,
                        decoration: const InputDecoration(
                          labelText: 'Route',
                          border: OutlineInputBorder(),
                        ),
                        items: widget.routes.map<DropdownMenuItem<String>>((route) => DropdownMenuItem<String>(
                          value: route['id']?.toString() ?? '',
                          child: Text(route['name'] ?? 'Unknown Route'),
                        )).toList(),
                        onChanged: (value) {
                          setState(() => _selectedRouteId = value);
                          _autoFillTimes(value);
                        },
                        validator: (value) {
                          if (value == null) return 'Route is required';
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedBusId,
                        decoration: const InputDecoration(
                          labelText: 'Bus',
                          border: OutlineInputBorder(),
                        ),
                        items: widget.buses.map<DropdownMenuItem<String>>((bus) => DropdownMenuItem<String>(
                          value: bus['id']?.toString() ?? '',
                          child: Text('Bus ${bus['busNumber'] ?? 'Unknown'}'),
                        )).toList(),
                        onChanged: (value) => setState(() => _selectedBusId = value),
                        validator: (value) {
                          if (value == null) return 'Bus is required';
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedDriverId,
                  decoration: const InputDecoration(
                    labelText: 'Driver',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.drivers.map<DropdownMenuItem<String>>((driver) => DropdownMenuItem<String>(
                    value: driver['id']?.toString() ?? '',
                    child: Text(driver['fullName'] ?? 'Unknown Driver'),
                  )).toList(),
                  onChanged: (value) => setState(() => _selectedDriverId = value),
                  validator: (value) {
                    if (value == null) return 'Driver is required';
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _departureTimeController,
                        decoration: const InputDecoration(
                          labelText: 'Departure Time',
                          hintText: 'e.g., 08:00',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Departure time is required';
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _arrivalTimeController,
                        decoration: const InputDecoration(
                          labelText: 'Arrival Time',
                          hintText: 'e.g., 09:30',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value?.isEmpty ?? true) return 'Arrival time is required';
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  'Operating Days',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF0F172A),
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _weekDays.map((day) {
                    final isSelected = _selectedDays.contains(day);
                    return AnimatedWidgets.scaleButton(
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            _selectedDays.remove(day);
                          } else {
                            _selectedDays.add(day);
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? const Color(0xFF06B6D4)
                              : const Color(0xFF06B6D4).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: const Color(0xFF06B6D4).withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          day.substring(0, 3),
                          style: TextStyle(
                            color: isSelected ? Colors.white : const Color(0xFF06B6D4),
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
                if (_selectedDays.isEmpty)
                  const Padding(
                    padding: EdgeInsets.only(top: 8),
                    child: Text(
                      'Please select at least one day',
                      style: TextStyle(
                        color: Color(0xFFEF4444),
                        fontSize: 12,
                      ),
                    ),
                  ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Cancel'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveSchedule,
                        child: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : Text(widget.schedule == null ? 'Add Schedule' : 'Update Schedule'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _saveSchedule() async {
    if (!_formKey.currentState!.validate() || _selectedDays.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      if (widget.schedule == null) {
        // Create new schedule
        await AdminDataService().createSchedule(
          routeId: _selectedRouteId!,
          busId: _selectedBusId!,
          driverId: _selectedDriverId!,
          departureTime: _departureTimeController.text,
          arrivalTime: _arrivalTimeController.text,
          days: _selectedDays,
        );
      } else {
        // Update existing schedule
        await AdminDataService().updateSchedule(
          scheduleId: widget.schedule!['id'],
          routeId: _selectedRouteId,
          busId: _selectedBusId,
          departureTime: _departureTimeController.text,
          arrivalTime: _arrivalTimeController.text,
          days: _selectedDays,
        );
      }

      Navigator.pop(context);
      widget.onScheduleAdded();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(widget.schedule == null ? 'Schedule added successfully' : 'Schedule updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to ${widget.schedule == null ? 'add' : 'update'} schedule: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}