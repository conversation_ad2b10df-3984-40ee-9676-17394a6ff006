import 'package:flutter/material.dart';
import 'package:admin_web/theme/app_theme.dart';
import '../services/admin_data_service.dart';

class RealSettingsScreen extends StatefulWidget {
  const RealSettingsScreen({Key? key}) : super(key: key);

  @override
  State<RealSettingsScreen> createState() => _RealSettingsScreenState();
}

class _RealSettingsScreenState extends State<RealSettingsScreen> {
  List<Map<String, dynamic>> _settings = [];
  bool _isLoading = true;
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await AdminDataService().getAllSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });
      
      // Initialize controllers
      for (var setting in settings) {
        _controllers[setting['key']] = TextEditingController(
          text: setting['value'] ?? '',
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load settings: $e')),
      );
    }
  }

  Future<void> _updateSetting(String key, String value) async {
    try {
      await AdminDataService().updateSetting(
        settingKey: key,
        settingValue: value,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Setting updated successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update setting: $e')),
      );
    }
  }

  Widget _buildSettingTile(Map<String, dynamic> setting) {
    final controller = _controllers[setting['key']]!;
    final type = setting['type'] ?? 'text';
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _formatSettingName(setting['key']),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            if (setting['description'] != null) ...[
              const SizedBox(height: 4),
              Text(
                setting['description'],
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInputField(controller, type),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () => _updateSetting(setting['key'], controller.text),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Update'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField(TextEditingController controller, String type) {
    switch (type) {
      case 'boolean':
        return DropdownButtonFormField<String>(
          value: controller.text.toLowerCase() == 'true' ? 'true' : 'false',
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: const [
            DropdownMenuItem(value: 'true', child: Text('Enabled')),
            DropdownMenuItem(value: 'false', child: Text('Disabled')),
          ],
          onChanged: (value) {
            controller.text = value ?? 'false';
          },
        );
      case 'number':
        return TextFormField(
          controller: controller,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          keyboardType: TextInputType.number,
        );
      case 'email':
        return TextFormField(
          controller: controller,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          keyboardType: TextInputType.emailAddress,
        );
      case 'phone':
        return TextFormField(
          controller: controller,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          keyboardType: TextInputType.phone,
        );
      case 'time':
        return TextFormField(
          controller: controller,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            hintText: 'HH:MM',
          ),
        );
      default:
        return TextFormField(
          controller: controller,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        );
    }
  }

  String _formatSettingName(String key) {
    return key
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSettings,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Configuration',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Configure system-wide settings and preferences.',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: _settings.isEmpty
                  ? const Center(child: Text('No settings found'))
                  : ListView.builder(
                      itemCount: _settings.length,
                      itemBuilder: (context, index) {
                        return _buildSettingTile(_settings[index]);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
