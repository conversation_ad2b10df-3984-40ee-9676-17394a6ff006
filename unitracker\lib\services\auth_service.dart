import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:unitracker/models/user.dart' as app_user;
import 'supabase_service.dart';

class AuthService extends ChangeNotifier {
  app_user.User? _currentUser;
  bool _isLoading = false;
  final SupabaseService _supabaseService = SupabaseService.instance;

  app_user.User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;

  AuthService() {
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    final supabaseUser = _supabaseService.currentUser;
    if (supabaseUser != null) {
      await _loadUserProfile(supabaseUser.id);
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      final profileData = await _supabaseService.getUserProfile(userId);
      if (profileData != null) {
        _currentUser = app_user.User.fromSupabase(profileData);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    }
  }

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      String loginEmail = email;

      // For driver login, convert driver ID to email format
      if (!email.contains('@')) {
        // This is a driver ID, convert to email format
        loginEmail = '${email.toLowerCase()}@unitracker.driver';
      }

      debugPrint('🔐 AuthService: Attempting login with email: $loginEmail');

      final response = await _supabaseService.signIn(
        email: loginEmail,
        password: password,
      );

      if (response.user != null) {
        debugPrint('✅ AuthService: Login successful, loading profile for: ${response.user!.id}');
        await _loadUserProfile(response.user!.id);
      } else {
        throw Exception('Login failed');
      }
    } catch (e) {
      debugPrint('❌ AuthService: Login failed: $e');
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signUp({
    required String name,
    required String email,
    required String studentId,
    required String university,
    required String department,
    required String password,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _supabaseService.signUp(
        email: email,
        password: password,
        data: {
          'full_name': name,
          'role': 'student',
        },
      );

      if (response.user != null) {
        // Create user profile in the users table
        await _supabaseService.createUserProfile({
          'id': response.user!.id,
          'email': email,
          'full_name': name,
          'role': 'student',
          'student_id': studentId,
          'university': university,
          'department': department,
          'is_active': true,
        });

        await _loadUserProfile(response.user!.id);
      } else {
        throw Exception('Registration failed');
      }
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signUpDriver({
    required String name,
    required String driverId,
    required String licenseNumber,
    required DateTime licenseExpirationDate,
    required String licenseImagePath,
    required String password,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      // For drivers, we'll use a generated email based on driver ID
      final driverEmail = '${driverId.toLowerCase()}@unitracker.driver';

      final response = await _supabaseService.signUp(
        email: driverEmail,
        password: password,
        data: {
          'full_name': name,
          'role': 'driver',
        },
      );

      if (response.user != null) {
        // Create driver profile in the users table
        await _supabaseService.createUserProfile({
          'id': response.user!.id,
          'email': driverEmail,
          'full_name': name,
          'role': 'driver',
          'driver_license': licenseNumber,
          'license_expiry': licenseExpirationDate.toIso8601String().split('T')[0],
          'is_active': true,
        });

        // Also create a record in the drivers table for admin panel
        debugPrint('🚗 AuthService: Creating driver record for: ${response.user!.id}');
        await _supabaseService.createDriverRecord({
          'id': response.user!.id,
          'full_name': name,
          'email': driverEmail,
          'driver_license': licenseNumber,
          'license_expiry': licenseExpirationDate.toIso8601String().split('T')[0],
          'driver_id': driverId,
          'is_active': true,
        });
        debugPrint('✅ AuthService: Driver record created successfully');

        await _loadUserProfile(response.user!.id);
      } else {
        throw Exception('Driver registration failed');
      }
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signOut() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _supabaseService.signOut();
      _currentUser = null;
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateProfile({
    String? name,
    String? email,
    String? studentId,
    String? university,
    String? department,
    String? profileImage,
  }) async {
    if (_currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Implement actual profile update logic
      await Future.delayed(const Duration(seconds: 1));

      _currentUser = app_user.User(
        id: _currentUser!.id,
        name: name ?? _currentUser!.name,
        email: email ?? _currentUser!.email,
        studentId: studentId ?? _currentUser!.studentId,
        university: university ?? _currentUser!.university,
        department: department ?? _currentUser!.department,
        role: _currentUser!.role,
        profileImage: profileImage ?? _currentUser!.profileImage,
      );
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentUser == null) {
      throw Exception('No user is currently logged in');
    }

    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Implement actual password change logic
      // For now, we'll simulate a successful password change
      await Future.delayed(const Duration(seconds: 1));

      // In a real implementation, you would:
      // 1. Send the current password and new password to your backend
      // 2. The backend would verify the current password against the stored hash
      // 3. If verified, update to the new password
      // 4. Handle any errors that might occur
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> resetPassword({
    required String email,
    required String driverId,
    required String role,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      // TODO: Implement actual password reset logic
      // For now, we'll simulate a successful password reset request
      await Future.delayed(const Duration(seconds: 1));

      // In a real implementation, you would:
      // 1. For students: Send a password reset link to their email
      // 2. For drivers: Send a password reset link to their registered phone number or email
      // 3. Handle any errors (invalid email, account not found, etc.)

      if (role == 'student' && !email.contains('@')) {
        throw Exception('Please enter a valid email address');
      }

      if (role == 'driver' && driverId.isEmpty) {
        throw Exception('Please enter your driver ID');
      }
    } catch (e) {
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateUserProfile(String userId, Map<String, dynamic> data) async {
    try {
      await _supabaseService.updateUserProfile(userId, data);
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      return await _supabaseService.getUserProfile(userId);
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }
}
