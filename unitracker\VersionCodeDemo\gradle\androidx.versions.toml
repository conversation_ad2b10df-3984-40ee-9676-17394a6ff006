###
### This file is a version catalog of AndroidX
### These values can be referenced with `androidxLibs` accessor
###   example: activity-ktx -> implementation androidxLibs.activity.ktx

[versions]
# https://developer.android.com/studio/releases/gradle-plugin
# This will be overriden by gradle.properties
android-gradle = "unspecified"

# https://developer.android.com/jetpack/androidx/versions
activity = "1.7.1"
annotation = "1.6.0"
annotation-experimental = '1.3.0'
appcompat = "1.6.1"
arch-core = "2.2.0"
benchmark = "1.1.1"
biometric = "1.1.0"
browser = "1.5.0"
camera = "1.2.2"
cardview = "1.0.0"
constraintlayout = "2.1.4"
constraintlayout-compose = "1.0.1"
compose-bom = '2023.05.00'
compose-compiler = "1.4.7"
concurrent = "1.1.0"
core = "1.10.1"
datastore = "1.0.0"
dynamicanimation = "1.0.0"
exifinterface = "1.3.6"
fragment = "1.5.7"
google-shortcuts = "1.0.1"
gridlayout = "1.0.0"
interpolator = "1.0.0"
legacy-support-v4 = "1.0.0"
legacy-support-v13 = "1.0.0"
lifecycle = "2.6.1"
loader = "1.1.0"
localbroadcastmanager = "1.1.0"
media2 = "1.1.3"
navigation = "2.5.3"
paging = "3.1.1"
palette = "1.0.0"
percentlayout = "1.0.0"
preference = "1.2.0"
recyclerview = "1.3.0"
recyclerview-selection = "1.1.0"
room = "2.5.1"
savedstate = "1.2.0"
slidingpanelayout = "1.2.0"
splashscreen = "1.0.0"
startup = "1.1.1"
swiperefreshlayout = "1.1.0"
work = "2.8.1"
test-annotation = "1.0.1"
test-core = "1.5.0"
test-espresso = "3.5.1"
test-ext-junit = "1.1.5"
test-ext-truth = "1.5.0"
test-monitor="1.6.1"
test-orchestrator="1.4.2"
test-services="1.4.2"
test-uiautomator = "2.2.0"
tracing = "1.1.0"
transition = "1.4.1"
vectordrawable = "1.1.0"
versionedparcelable = "1.1.1"
viewpager2 = "1.0.0"
webkit = "1.6.1"
wear-remote-interaction = "1.0.0"
window = "1.1.0-rc01"

[libraries]

activity-ktx = { module = "androidx.activity:activity-ktx", version.ref = "activity" }
activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activity" }
annotation = { module = "androidx.annotation:annotation", version.ref = "annotation" }
annotation-experimental = { module = "androidx.annotation:annotation-experimental", version.ref = "annotation-experimental" }
appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }

arch-core-common = { module = "androidx.arch.core:core-common", version.ref = "arch-core" }
arch-core-runtime = { module = "androidx.arch.core:core-runtime", version.ref = "arch-core" }
arch-core-testing = { module = "androidx.arch.core:core-testing", version.ref = "arch-core" }

benchmark-junit4 = { module = "androidx.benchmark:benchmark-junit4", version.ref = "benchmark" }
benchmark-macro-junit4 = { module = "androidx.benchmark:benchmark-macro-junit4", version.ref = "benchmark" }

biometric = { module = "androidx.biometric:biometric", version.ref = "biometric" }

browser = { module = "androidx.browser:browser", version.ref = "browser" }

camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "camera" }
camera-core = { module = "androidx.camera:camera-core", version.ref = "camera" }
camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camera" }

cardview = { module = "androidx.cardview:cardview", version.ref = "cardview" }

compose-bom = { module = "androidx.compose:compose-bom", version.ref = "compose-bom" }
compose-animation-graphics = { module = "androidx.compose.animation:animation-graphics"}
compose-compiler = { module = "androidx.compose.compiler:compiler", version.ref = "compose-compiler" }
compose-foundation = { module = "androidx.compose.foundation:foundation"}
compose-foundation-layout = { module = "androidx.compose.foundation:foundation-layout"}
compose-material = { module = "androidx.compose.material:material"}
compose-material-icons-extended = { module = "androidx.compose.material:material-icons-extended"}
compose-material3 = { module = "androidx.compose.material3:material3"}
# class is reserved keyword, so removed -class suffix
compose-material3-window-size = { module = "androidx.compose.material3:material3-window-size-class"}
compose-runtime = { module = "androidx.compose.runtime:runtime"}
compose-runtime-livedata = { module = "androidx.compose.runtime:runtime-livedata"}
# rxjava2 is obsoleted, and expose rxjava3 binding only
#compose-runtime-rxjava2 = { module = "androidx.compose.runtime:runtime-rxjava2"}
compose-runtime-rxjava3 = { module = "androidx.compose.runtime:runtime-rxjava3"}
compose-ui = { module = "androidx.compose.ui:ui"}
compose-ui-text-google-fonts = { module = "androidx.compose.ui:ui-text-google-fonts"}
compose-ui-util = { module = "androidx.compose.ui:ui-util"}
compose-ui-viewbinding = { module = "androidx.compose.ui:ui-viewbinding"}
compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling"}
compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview"}
compose-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4"}
compose-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest"}

concurrent-futures-ktx = { module = "androidx.concurrent:concurrent-futures-ktx", version.ref = "concurrent" }

constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayout-compose" }

core = { module = "androidx.core:core", version.ref = "core" }
core-ktx = { module = "androidx.core:core-ktx", version.ref = "core" }
core-google-shortcuts =  { module = "androidx.core:core-google-shortcuts", version.ref = "google-shortcuts" }
core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "splashscreen" }

databinding-adapters = { module = "androidx.databinding:databinding-adapters", version.ref = "android-gradle" }
databinding-common = { module = "androidx.databinding:databinding-common", version.ref = "android-gradle" }
databinding-ktx = { module = "androidx.databinding:databinding-ktx", version.ref = "android-gradle" }
databinding-runtime = { module = "androidx.databinding:databinding-runtime", version.ref = "android-gradle" }

datastore = { module = "androidx.datastore:datastore", version.ref = "datastore" }
datastore-rxjava3 = { module = "androidx.datastore:datastore-rxjava3", version.ref = "datastore" }
datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastore" }
datastore-preferences-rxjava3 = { module = "androidx.datastore:datastore-preferences-rxjava3", version.ref = "datastore" }

dynamicanimation = { module = "androidx.dynamicanimation:dynamicanimation", version.ref = "dynamicanimation" }

exifinterface = { module = "androidx.exifinterface:exifinterface", version.ref = "exifinterface" }

fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragment" }
fragment-testing = { module = "androidx.fragment:fragment-testing", version.ref = "fragment" }

gridlayout = { module = "androidx.gridlayout:gridlayout", version.ref = "gridlayout" }

interpolator = { module = "androidx.interpolator:interpolator", version.ref = "interpolator" }

legacy-support-v4 = { module = "androidx.legacy:legacy-support-v4", version.ref = "legacy-support-v4" }
legacy-support-v13 = { module = "androidx.legacy:legacy-support-v13", version.ref = "legacy-support-v13" }

lifecycle-common = { module = "androidx.lifecycle:lifecycle-common", version.ref = "lifecycle" }
lifecycle-common-java8 = { module = "androidx.lifecycle:lifecycle-common-java8", version.ref = "lifecycle" }
lifecycle-livedata-core-ktx = { module = "androidx.lifecycle:lifecycle-livedata-core-ktx", version.ref = "lifecycle" }
lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycle" }
lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "lifecycle" }
lifecycle-reactivestreams-ktx = { module = "androidx.lifecycle:lifecycle-reactivestreams-ktx", version.ref = "lifecycle" }
lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle" }
lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "lifecycle" }
lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycle" }
lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
lifecycle-viewmodel-savedstate = { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate", version.ref = "lifecycle" }

loader = { module = "androidx.loader:loader", version.ref = "loader" }

localbroadcastmanager =  { module = "androidx.localbroadcastmanager:localbroadcastmanager", version.ref = "localbroadcastmanager" }

media2-player = { module = "androidx.media2:media2-player", version.ref = "media2" }
media2-session = { module = "androidx.media2:media2-session", version.ref = "media2" }
media2-widget = { module = "androidx.media2:media2-widget", version.ref = "media2" }

navigation-compose = { module = "androidx.navigation:navigation", version.ref = "navigation" }
navigation-fragment = { module = "androidx.navigation:navigation-fragment", version.ref = "navigation" }
navigation-ui = { module = "androidx.navigation:navigation-ui", version.ref = "navigation" }
navigation-dynamic-features-fragment = { module = "androidx.navigation:navigation-dynamic-features-fragment", version.ref = "navigation" }
navigation-safeargs-gradle = { module = "androidx.navigation:navigation-safe-args-gradle-plugin", version.ref = "navigation" }
navigation-testing = { module = "androidx.navigation:navigation-testing", version.ref = "navigation" }

paging-common = { module = "androidx.paging:paging-common", version.ref = "paging" }
paging-guava = { module = "androidx.paging:paging-guava", version.ref = "paging" }
paging-runtime-ktx = { module = "androidx.paging:paging-runtime-ktx", version.ref = "paging" }
paging-rxjava2 = { module = "androidx.paging:paging-rxjava2", version.ref = "paging" }
paging-rxjava3 = { module = "androidx.paging:paging-rxjava3", version.ref = "paging" }

palette = { module = "androidx.palette:palette", version.ref = "palette" }

percentlayout = { module = "androidx.percentlayout:percentlayout", version.ref = "percentlayout" }

preference-ktx = { module = "androidx.preference:preference-ktx", version.ref = "preference" }

recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
recyclerview-selection = { module = "androidx.recyclerview:recyclerview-selection", version.ref = "recyclerview-selection" }

room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
room-rxjava2 = { module = "androidx.room:room-rxjava2", version.ref = "room" }
room-rxjava3 = { module = "androidx.room:room-rxjava3", version.ref = "room" }
room-testing = { module = "androidx.room:room-testing", version.ref = "room" }
room-paging = { module = "androidx.room:room-paging", version.ref = "room" }

savedstate-ktx = { module = "androidx.savedstate:savedstate-ktx", version.ref = "savedstate" }

slidingpanelayout = { module = "androidx.slidingpanelayout:slidingpanelayout", version.ref = "slidingpanelayout" }

startup-runtime = { module = "androidx.startup:startup-runtime", version.ref = "startup" }

swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version.ref = "swiperefreshlayout" }

test-annotation = { module = "androidx.test:annotation", version.ref = "test-annotation" }
test-ktx = { module = "androidx.test:core-ktx", version.ref = "test-core" }
test-monitor = { module = "androidx.test:monitor", version.ref = "test-monitor" }
test-orchestrator = { module = "androidx.test:orchestrator", version.ref = "test-orchestrator" }
test-rules = { module = "androidx.test:rules", version.ref = "test-core" }
test-runner = { module = "androidx.test:runner", version.ref = "test-core" }
test-services = { module = "androidx.test.services:test-services", version.ref = "test-services" }
test-services-storage = { module = "androidx.test.services:storage", version.ref = "test-services" }

test-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "test-espresso" }
test-espresso-contrib = { module = "androidx.test.espresso:espresso-contrib", version.ref = "test-espresso" }
test-espresso-intents = { module = "androidx.test.espresso:espresso-intents", version.ref = "test-espresso" }
test-espresso-web = { module = "androidx.test.espresso:espresso-web", version.ref = "test-espresso" }

test-ext-junit-ktx = { module = "androidx.test.ext:junit-ktx", version.ref = "test-ext-junit" }
test-ext-truth = { module = "androidx.test.ext:truth", version.ref = "test-ext-truth" }

test-uiautomator = { module = "androidx.test.uiautomator:uiautomator", version.ref = "test-uiautomator" }

tracing-ktx = { module = "androidx.tracing:tracing-ktx", version.ref = "tracing" }

transition-ktx = { module = "androidx.transition:transition-ktx", version.ref = "transition" }

vectordrawable = { module = "androidx.vectordrawable:vectordrawable", version.ref = "vectordrawable" }
vectordrawable-animated = { module = "androidx.vectordrawable:vectordrawable-animated", version.ref = "vectordrawable" }
vectordrawable-seekable = { module = "androidx.vectordrawable:vectordrawable-seekable", version.ref = "vectordrawable" }

versionedparcelable = { module = "androidx.versionedparcelable:versionedparcelable", version.ref = "versionedparcelable" }

viewpager2 = { module = "androidx.viewpager2:viewpager2", version.ref = "viewpager2"}

webkit = { module = "androidx.webkit:webkit", version.ref = "webkit"}

work-gcm = { module = "androidx.work:work-gcm", version.ref = "work"}
work-multiprocess = { module = "androidx.work:work-multiprocess", version.ref = "work"}
work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "work"}
work-rxjava2 = { module = "androidx.work:work-rxjava2", version.ref = "work"}
work-rxjava3 = { module = "androidx.work:work-rxjava3", version.ref = "work"}
work-testing = { module = "androidx.work:work-testing", version.ref = "work"}

wear-remote-interaction = { module = "androidx.wear:wear-remote-interactions", version.ref = "wear-remote-interaction"}

window = { module = "androidx.window:window", version.ref = "window" }
window-rxjava3 = { module = "androidx.window:window-rxjava3", version.ref = "window" }
window-testing = { module = "androidx.window:window-testing", version.ref = "window" }

[plugins]
navigation-safeargs = { id = "androidx.navigation.safeargs", version.ref = "navigation" }
navigation-safeargs-kotlin = { id = "androidx.navigation.safeargs.kotlin", version.ref = "navigation" }

[bundles]
compose = [
    "compose-runtime",
    "compose-runtime-livedata",
    "compose-foundation",
    "compose-foundation-layout",
    "compose-ui",
    "compose-ui-util",
    "compose-ui-viewbinding",
    "compose-material",
]

test = [
    "test-ktx",
    "test-annotation",
    "test-rules",
    "test-runner",
    "test-ext-junit-ktx",
    "test-ext-truth",
    "arch-core-testing",
]

espresso = [
    "test-espresso-core",
    "test-espresso-contrib",
    "test-espresso-intents",
    "test-espresso-web",
    "test-uiautomator",
]
