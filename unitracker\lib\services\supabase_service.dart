import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance {
    _instance ??= SupabaseService._internal();
    return _instance!;
  }

  SupabaseService._internal();

  SupabaseClient get client => Supabase.instance.client;

  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
  }

  // Auth methods
  User? get currentUser => client.auth.currentUser;
  bool get isAuthenticated => currentUser != null;

  Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    return await client.auth.signUp(
      email: email,
      password: password,
      data: data,
    );
  }

  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    return await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    await client.auth.signOut();
  }

  // User profile methods
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    final response = await client
        .from('users')
        .select()
        .eq('id', userId)
        .maybeSingle();
    return response;
  }

  Future<void> updateUserProfile(String userId, Map<String, dynamic> data) async {
    await client
        .from('users')
        .update(data)
        .eq('id', userId);
  }

  Future<void> createUserProfile(Map<String, dynamic> data) async {
    await client.from('users').insert(data);
  }

  Future<void> createDriverRecord(Map<String, dynamic> data) async {
    await client.from('drivers').insert(data);
  }

  // Routes methods
  Future<List<Map<String, dynamic>>> getRoutes() async {
    final response = await client
        .from('routes')
        .select('*, bus_stops(*)')
        .eq('is_active', true)
        .order('name');
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>?> getRoute(String routeId) async {
    final response = await client
        .from('routes')
        .select('*, bus_stops(*)')
        .eq('id', routeId)
        .maybeSingle();
    return response;
  }

  // Reservations methods
  Future<List<Map<String, dynamic>>> getUserReservations(String userId) async {
    final response = await client
        .from('reservations')
        .select('''
          *,
          routes(*),
          buses(*),
          pickup_stop:bus_stops!pickup_stop_id(*),
          drop_stop:bus_stops!drop_stop_id(*)
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>?> createReservation(Map<String, dynamic> data) async {
    try {
      debugPrint('🎫 SupabaseService: Creating reservation with data: $data');
      final response = await client
          .from('reservations')
          .insert(data)
          .select('''
            *,
            routes(*),
            pickup_stop:bus_stops!pickup_stop_id(*),
            drop_stop:bus_stops!drop_stop_id(*)
          ''')
          .single();
      debugPrint('✅ SupabaseService: Reservation created successfully: $response');
      return response;
    } catch (e) {
      debugPrint('❌ SupabaseService: Failed to create reservation: $e');
      return null;
    }
  }

  Future<void> updateReservation(String reservationId, Map<String, dynamic> data) async {
    await client
        .from('reservations')
        .update(data)
        .eq('id', reservationId);
  }

  Future<void> cancelReservation(String reservationId) async {
    await client
        .from('reservations')
        .update({'status': 'cancelled'})
        .eq('id', reservationId);
  }

  // Notifications methods
  Future<List<Map<String, dynamic>>> getUserNotifications(String userId) async {
    final response = await client
        .from('notifications')
        .select()
        .eq('user_id', userId)
        .order('created_at', ascending: false);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<void> createNotification(Map<String, dynamic> data) async {
    await client.from('notifications').insert(data);
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    await client
        .from('notifications')
        .update({'is_read': true})
        .eq('id', notificationId);
  }

  // Bus tracking methods
  Future<List<Map<String, dynamic>>> getBusLocations(String routeId) async {
    final response = await client
        .from('bus_locations')
        .select('*, buses(*)')
        .eq('buses.route_id', routeId)
        .order('timestamp', ascending: false);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<void> updateBusLocation(String busId, Map<String, dynamic> locationData) async {
    await client.from('bus_locations').upsert({
      'bus_id': busId,
      ...locationData,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  // Schedules methods
  Future<List<Map<String, dynamic>>> getSchedules(String routeId) async {
    final response = await client
        .from('schedules')
        .select('*, routes(*), buses(*)')
        .eq('route_id', routeId)
        .eq('is_active', true)
        .order('departure_time');
    return List<Map<String, dynamic>>.from(response);
  }

  // Real-time subscriptions
  RealtimeChannel subscribeToUserNotifications(String userId, Function(Map<String, dynamic>) onData) {
    return client
        .channel('user_notifications_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'notifications',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) => onData(payload.newRecord),
        )
        .subscribe();
  }

  RealtimeChannel subscribeToBusLocations(String routeId, Function(Map<String, dynamic>) onData) {
    return client
        .channel('bus_locations_$routeId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'bus_locations',
          callback: (payload) => onData(payload.newRecord),
        )
        .subscribe();
  }

  RealtimeChannel subscribeToReservations(String userId, Function(Map<String, dynamic>) onData) {
    return client
        .channel('user_reservations_$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'reservations',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) => onData(payload.newRecord),
        )
        .subscribe();
  }

  // Driver-specific methods
  Future<Map<String, dynamic>?> getDriverAssignment(String driverId) async {
    debugPrint('🔍 SupabaseService: Querying driver assignment for: $driverId');
    final response = await client
        .from('driver_assignments')
        .select('''
          *,
          routes(*),
          buses(*)
        ''')
        .eq('driver_id', driverId)
        .eq('is_active', true)
        .maybeSingle();
    debugPrint('🔍 SupabaseService: Query response: $response');
    return response;
  }

  Future<List<Map<String, dynamic>>> getDriverTripHistory(String driverId, {int limit = 50}) async {
    final response = await client
        .from('trip_history')
        .select('''
          *,
          routes(name),
          buses(bus_number)
        ''')
        .eq('driver_id', driverId)
        .order('trip_date', ascending: false)
        .limit(limit);
    return List<Map<String, dynamic>>.from(response);
  }

  Future<Map<String, dynamic>> completeTrip({
    required String driverId,
    required String routeId,
    required String busId,
    required DateTime startTime,
    required DateTime endTime,
    required int passengerCount,
    required double distanceKm,
    required bool onTime,
    String? notes,
  }) async {
    final response = await client
        .from('trip_history')
        .insert({
          'driver_id': driverId,
          'route_id': routeId,
          'bus_id': busId,
          'trip_date': DateTime.now().toIso8601String().split('T')[0],
          'start_time': startTime.toIso8601String(),
          'end_time': endTime.toIso8601String(),
          'passenger_count': passengerCount,
          'distance_km': distanceKm,
          'on_time': onTime,
          'notes': notes,
          'status': 'completed',
        })
        .select()
        .single();
    return response;
  }

  Future<Map<String, dynamic>> getDriverStats(String driverId) async {
    // Get trip statistics
    final statsResponse = await client
        .from('trip_history')
        .select('*')
        .eq('driver_id', driverId);

    final trips = List<Map<String, dynamic>>.from(statsResponse);

    final totalTrips = trips.length;
    final onTimeTrips = trips.where((trip) => trip['on_time'] == true).length;
    final onTimePerformance = totalTrips > 0 ? (onTimeTrips / totalTrips) * 100 : 0.0;

    // Calculate average passenger count
    final totalPassengers = trips.fold<int>(0, (sum, trip) => sum + (trip['passenger_count'] as int? ?? 0));
    final avgPassengers = totalTrips > 0 ? totalPassengers / totalTrips : 0.0;

    return {
      'total_trips': totalTrips,
      'on_time_performance': onTimePerformance,
      'average_passengers': avgPassengers,
      'total_distance': trips.fold<double>(0, (sum, trip) => sum + (trip['distance_km'] as double? ?? 0)),
      'rating': 4.5, // Mock rating for now
      'safety_score': 95.0, // Mock safety score
      'customer_satisfaction': 4.3, // Mock satisfaction
    };
  }

  Future<void> updateDriverLocation({
    required String driverId,
    required double latitude,
    required double longitude,
    required double speed,
    required bool isMoving,
  }) async {
    // Get driver's current bus assignment
    final assignment = await getDriverAssignment(driverId);
    if (assignment != null && assignment['bus_id'] != null) {
      await updateBusLocation(assignment['bus_id'], {
        'latitude': latitude,
        'longitude': longitude,
        'speed': speed,
        'is_moving': isMoving,
        'heading': 0, // Can be calculated from GPS
      });
    }
  }
}
