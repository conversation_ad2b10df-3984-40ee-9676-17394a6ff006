plugins {
    id("com.android.dynamic-feature")
    id("org.jetbrains.kotlin.android")
}

android {
    namespace "com.example.dynamicfeature1"
    defaultConfig {
    }

    flavorDimensions 'default'
    productFlavors {
        develop { }
        beta { }
        staging { }
        production { }
    }
}

dependencies {
    implementation project(':app')
    implementation libs.kotlin.stdlib.jdk7
    implementation androidxLibs.appcompat
    implementation androidxLibs.constraintlayout
    implementation androidxLibs.room.ktx
    implementation androidxLibs.room.runtime
}
