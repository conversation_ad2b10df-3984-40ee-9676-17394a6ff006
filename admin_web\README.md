# admin_web

A Flutter web application for the University Bus Tracker Admin Dashboard.

## Getting Started

This project is a standalone web admin panel built with Flutter.

### Prerequisites
- [Flutter SDK](https://docs.flutter.dev/get-started/install) (version 3.0.0 or higher)
- Chrome or another supported web browser

### Setup
1. Clone the repository or copy the project files.
2. Install dependencies:
   ```sh
   flutter pub get
   ```
3. Run the app in your browser:
   ```sh
   flutter run -d chrome
   ```

### Build for Production
To build the web app for deployment:
```sh
flutter build web
```
The output will be in the `build/web/` directory.

---

For more information, see the [Flutter web documentation](https://docs.flutter.dev/platform-integration/web).
