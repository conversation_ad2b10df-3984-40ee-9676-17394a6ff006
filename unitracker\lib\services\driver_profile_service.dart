import 'package:flutter/foundation.dart';
import '../models/user.dart';
import 'supabase_service.dart';

class DriverProfileService extends ChangeNotifier {
  static final DriverProfileService _instance = DriverProfileService._internal();
  static DriverProfileService get instance => _instance;
  
  DriverProfileService._internal();

  final SupabaseService _supabaseService = SupabaseService.instance;
  
  User? _driverProfile;
  bool _isLoading = false;
  String? _error;

  User? get driverProfile => _driverProfile;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadDriverProfile(String driverId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final profileData = await _supabaseService.getUserProfile(driverId);
      if (profileData != null) {
        _driverProfile = User(
          id: profileData['id'] ?? driverId,
          name: profileData['full_name'] ?? 'Unknown Driver',
          email: profileData['email'] ?? '',
          studentId: profileData['student_id'] ?? '',
          university: profileData['university'] ?? '',
          department: profileData['department'] ?? '',
          role: profileData['role'] ?? 'driver',
          profileImage: profileData['profile_image_url'],
          driverId: profileData['driver_id'],
          licenseNumber: profileData['license_number'],
          licenseExpiration: profileData['license_expiration'],
        );
      }
    } catch (e) {
      _error = 'Failed to load driver profile: $e';
      debugPrint('Error loading driver profile: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateDriverProfile(Map<String, dynamic> updates) async {
    if (_driverProfile == null) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _supabaseService.updateUserProfile(_driverProfile!.id, updates);
      
      // Update local profile
      _driverProfile = User(
        id: _driverProfile!.id,
        name: updates['full_name'] ?? _driverProfile!.name,
        email: updates['email'] ?? _driverProfile!.email,
        studentId: updates['student_id'] ?? _driverProfile!.studentId,
        university: updates['university'] ?? _driverProfile!.university,
        department: updates['department'] ?? _driverProfile!.department,
        role: _driverProfile!.role,
        profileImage: updates['profile_image_url'] ?? _driverProfile!.profileImage,
        driverId: updates['driver_id'] ?? _driverProfile!.driverId,
        licenseNumber: updates['license_number'] ?? _driverProfile!.licenseNumber,
        licenseExpiration: updates['license_expiration'] ?? _driverProfile!.licenseExpiration,
      );
    } catch (e) {
      _error = 'Failed to update driver profile: $e';
      debugPrint('Error updating driver profile: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearProfile() {
    _driverProfile = null;
    _error = null;
    notifyListeners();
  }
}
