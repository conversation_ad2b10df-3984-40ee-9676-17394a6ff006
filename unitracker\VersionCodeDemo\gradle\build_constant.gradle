import java.util.Properties
import java.nio.file.Files

def BUILD_MAJOR = "MAJOR"
def BUILD_MINOR = "MINOR"
def BUILD_MICRO = "MICRO"

// https://developer.android.com/studio/publish/versioning.html#appversioning
def VERSION_CODE_MAX = 2100000000
def VERSION_CODE_BASE_MAJOR = 10000000
def VERSION_CODE_BASE_MINOR = 100000
def VERSION_CODE_BASE_MICRO = 10000
def VERSION_CODE_BASE_BUILD_NUMBER = 1

def versionProperties = new Properties()
try (br = Files.newBufferedReader(rootProject.file("version.properties").toPath())) {
    versionProperties.load(br)
}

def checkMaxVersion = { Integer version, BigDecimal maxVersion, String fieldName ->
    if (version >= maxVersion) {
        throw IllegalArgumentException("$fieldName must be lesser than $maxVersion")
    }
}

def generateVersionCode = {
    def major = versionProperties.getProperty(BUILD_MAJOR).toInteger()
    def minor = versionProperties.getProperty(BUILD_MINOR).toInteger()
    def micro = versionProperties.getProperty(BUILD_MICRO).toInteger()
    def buildNumber = (System.getenv("BUILD_NUMBER") ?: "0").toInteger()

    checkMaxVersion(major, VERSION_CODE_MAX / VERSION_CODE_BASE_MAJOR, "Major version")
    checkMaxVersion(minor, VERSION_CODE_BASE_MAJOR / VERSION_CODE_BASE_MINOR, "Minor version")
    checkMaxVersion(micro, VERSION_CODE_BASE_MINOR / VERSION_CODE_BASE_MICRO, "Micro version")
    checkMaxVersion(buildNumber, VERSION_CODE_BASE_MICRO / VERSION_CODE_BASE_BUILD_NUMBER, "Build Number")

    return major * VERSION_CODE_BASE_MAJOR +
        minor * VERSION_CODE_BASE_MINOR +
        micro * VERSION_CODE_BASE_MICRO +
        buildNumber * VERSION_CODE_BASE_BUILD_NUMBER
}

ext {
    versionCode = generateVersionCode()
    versionName =  [BUILD_MAJOR, BUILD_MINOR, BUILD_MICRO].collect {
        versionProperties.getProperty(it)
    }.join(".")
}
