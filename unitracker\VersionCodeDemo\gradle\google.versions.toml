[versions]
# https://developers.google.com/android/guides/google-services-plugin
# https://github.com/google/play-services-plugins/releases
gms-services = "4.3.14"

# https://developers.google.com/android/guides/opensource
gms-oss-licenses = "0.10.5"

# https://firebase.google.com/support/release-notes/android
firebase = "31.0.1"
firebase-crashlytics-gradle = "2.9.2"

# https://developer.android.com/reference/com/google/android/play/core/release-notes
play-core = "1.10.3"

# https://developers.google.com/ml-kit/release-notes#android-api-latest-versions
mlkit-language = "17.0.3"

# https://developers.google.com/android/guides/releases
play-services-ads = "21.3.0"
play-services-ads-identifier = "18.0.1"
play-services-auth = "20.3.0"
play-services-base = "17.2.1"
play-services-location = "21.0.0"
play-services-maps = "18.1.0"
play-services-osslicenses = "17.0.0"
play-services-vision = "20.1.3"
play-services-wearable = "18.0.0"

# https://github.com/google/accompanist/releases
accompanist = "0.27.0"

# https://developers.google.com/interactive-media-ads/docs/sdks/android/client-side/history
ads-interactivemedia-v3 = "3.27.0"

# https://github.com/googleapis/google-api-java-client/releases
api-client-android = "1.32.1"
# https://github.com/googleapis/google-api-java-client-services
api-client-services-drive = "v3-rev20210725-1.32.1"
api-client-services-youtube = "v3-rev20210811-1.32.1"

# https://github.com/google/dagger/releases
dagger = "2.42"

# https://github.com/google/exoplayer/releases
exoplayer = "2.18.1"

# https://github.com/google/auto/releases
auto-service = "1.0.1"

# https://github.com/ZacSweers/auto-service-ksp/releases
# FIXME: Will be merged to auto-service
auto-serviceksp = "1.0.0"

# https://github.com/google/flexbox-layout/releases
flexbox = "1.1.1"

# https://github.com/material-components/material-components-android/releases
material = "1.5.0"

# https://github.com/material-components/material-components-android-compose-theme-adapter/releases
material-compose-theme-adapter = "1.1.21"
material-compose-theme-adapter3 = "1.0.21"

# https://github.com/google/gson/blob/master/CHANGELOG.md
gson = "2.9.1"

# https://github.com/google/guava/releases
guava = "31.0.1-android"

# https://github.com/google/truth/releases
truth = "1.1.3"


[libraries]
gms-services = { module = "com.google.gms:google-services", version.ref = "gms-services" }
gms-oss-licenses = { module = "com.google.android.gms:oss-licenses-plugin", version.ref = "gms-oss-licenses" }

firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebase" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging", version = "" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics", version = "" }
firebase-crashlytics-gradle = { module = "com.google.firebase:firebase-crashlytics-gradle", version.ref = "firebase-crashlytics-gradle" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics", version = "" }

api-client-android = { module = "com.google.api-client:google-api-client-android", version.ref = "api-client-android" }
api-client-services-drive = { module = "com.google.apis:google-api-services-drive", version.ref = "api-client-services-drive" }
api-client-services-youtube = { module = "com.google.apis:google-api-services-youtube", version.ref = "api-client-services-youtube" }

mlkit-language-id = { module = "com.google.mlkit:language-id", version.ref = "mlkit-language" }

play-services-ads = { module = "com.google.android.gms:play-services-ads", version.ref = "play-services-ads" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version.ref = "play-services-ads-identifier" }
play-services-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "play-services-auth" }
play-services-base = { module = "com.google.android.gms:play-services-base", version.ref = "play-services-base" }
play-services-location = { module = "com.google.android.gms:play-services-location", version.ref = "play-services-location" }
play-services-maps = { module = "com.google.android.gms:play-services-maps", version.ref = "play-services-maps" }
play-services-osslicenses = { module = "com.google.android.gms:play-services-oss-licenses", version.ref = "play-services-osslicenses" }
play-services-vision = { module = "com.google.android.gms:play-services-vision", version.ref = "play-services-vision" }
play-services-wearable = { module = "com.google.android.gms:play-services-wearable", version.ref = "play-services-wearable" }

accompanist-appcompat-theme = { module = "com.google.accompanist:accompanist-appcompat-theme", version.ref = "accompanist" }
accompanist-drawablepainter = { module = "com.google.accompanist:accompanist-drawablepainter", version.ref = "accompanist" }
accompanist-flowlayout = { module = "com.google.accompanist:accompanist-flowlayout", version.ref = "accompanist" }
accompanist-insets = { module = "com.google.accompanist:accompanist-insets", version.ref = "accompanist" }
accompanist-insets-ui = { module = "com.google.accompanist:accompanist-insets-ui", version.ref = "accompanist" }
accompanist-navigation-animation = { module = "com.google.accompanist:accompanist-navigation-animation", version.ref = "accompanist" }
accompanist-navigation-material = { module = "com.google.accompanist:accompanist-navigation-material", version.ref = "accompanist" }
accompanist-pager = { module = "com.google.accompanist:accompanist-pager", version.ref = "accompanist" }
accompanist-pager-indicators = { module = "com.google.accompanist:accompanist-indicators", version.ref = "accompanist" }
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanist" }
accompanist-placeholder = { module = "com.google.accompanist:accompanist-placeholder", version.ref = "accompanist" }
accompanist-placeholder-material = { module = "com.google.accompanist:accompanist-placeholder-material", version.ref = "accompanist" }
accompanist-swiperefresh = { module = "com.google.accompanist:accompanist-swiperefresh", version.ref = "accompanist" }
accompanist-systemuicontroller = { module = "com.google.accompanist:accompanist-systemuicontroller", version.ref = "accompanist" }

ads-interactivemedia-v3 = { module = "com.google.ads.interactivemedia.v3:interactivemedia", version.ref = "ads-interactivemedia-v3" }

dagger-android = { module = "com.google.dagger:dagger-android", version.ref = "dagger" }
dagger-android-processor = { module = "com.google.dagger:dagger-android-processor", version.ref = "dagger" }
dagger-android-support = { module = "com.google.dagger:dagger-android-support", version.ref = "dagger" }
dagger-compiler = { module = "com.google.dagger:dagger-compiler", version.ref = "dagger" }

exoplayer-core = { module = "com.google.android.exoplayer:exoplayer-core", version.ref = "exoplayer" }
exoplayer-dash = { module = "com.google.android.exoplayer:exoplayer-dash", version.ref = "exoplayer" }
exoplayer-extractor = { module = "com.google.android.exoplayer:exoplayer-extractor", version.ref = "exoplayer" }
exoplayer-hls = { module = "com.google.android.exoplayer:exoplayer-hls", version.ref = "exoplayer" }
exoplayer-robolectricutils = { module = "com.google.android.exoplayer:exoplayer-robolectricutils", version.ref = "exoplayer" }
exoplayer-rtsp = { module = "com.google.android.exoplayer:exoplayer-rtsp", version.ref = "exoplayer" }
exoplayer-smoothstreaming = { module = "com.google.android.exoplayer:exoplayer-smoothstreaming", version.ref = "exoplayer" }
exoplayer-testutils = { module = "com.google.android.exoplayer:exoplayer-testutils", version.ref = "exoplayer" }
exoplayer-transformer = { module = "com.google.android.exoplayer:exoplayer-transformer", version.ref = "exoplayer" }
exoplayer-ui = { module = "com.google.android.exoplayer:exoplayer-ui", version.ref = "exoplayer" }
exoplayer-ext-cronet = { module = "com.google.android.exoplayer:extension-cronet", version.ref = "exoplayer" }
exoplayer-ext-jobdispatcher = { module = "com.google.android.exoplayer:extension-jobdispatcher", version.ref = "exoplayer" }
exoplayer-ext-media2 = { module = "com.google.android.exoplayer:extension-media2", version.ref = "exoplayer" }
exoplayer-ext-mediasession = { module = "com.google.android.exoplayer:extension-mediasession", version.ref = "exoplayer" }
exoplayer-ext-okhttp = { module = "com.google.android.exoplayer:extension-okhttp", version.ref = "exoplayer" }
exoplayer-ext-workmanager = { module = "com.google.android.exoplayer:extension-workmanager", version.ref = "exoplayer" }

auto-service = { module = "com.google.auto.service:auto-service", version.ref = "auto-service" }
auto-service-kapt = { module = "com.google.auto.service:auto-service", version.ref = "auto-service" }
auto-service-annotations = { module = "com.google.auto.service:auto-service-annotations", version.ref = "auto-service" }
auto-service-ksp = { module = "dev.zacsweers.autoservice:auto-service-ksp", version.ref = "auto-serviceksp" }

gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }

flexbox = { module = "com.google.android:flexbox", version.ref = "flexbox" }
material = { module = "com.google.android.material:material", version.ref = "material" }
material-compose-theme-adapter = { module = "com.google.android.material:compose-theme-adapter", version.ref = "material-compose-theme-adapter" }
material-compose-theme-adapter3 = { module = "com.google.android.material:compose-theme-adapter-3", version.ref = "material-compose-theme-adapter3" }

play-core = { module = "com.google.android.play:core", version.ref = "play-core" }

truth = { module = "com.google.truth:truth", version.ref = "truth" }
