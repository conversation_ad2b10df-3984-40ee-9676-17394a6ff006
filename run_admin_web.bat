@echo off
echo Starting UniTracker Admin Web App...

REM Navigate to the admin_web directory
cd /d "%~dp0admin_web"

REM Check if we're in the right directory
if not exist "pubspec.yaml" (
    echo Error: pubspec.yaml not found. Make sure you're in the correct directory.
    pause
    exit /b 1
)

echo Cleaning Flutter project...
flutter clean

echo Getting dependencies...
flutter pub get

echo Starting Flutter web app...
flutter run -d chrome --web-port=3001

pause
