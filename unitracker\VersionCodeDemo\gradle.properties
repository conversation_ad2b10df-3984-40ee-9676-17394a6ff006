# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2g -XX:+UseParallelGC

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.caching=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=false

# Default Build Features configuration
android.defaults.buildfeatures.aidl=false
android.defaults.buildfeatures.buildconfig=false
android.defaults.buildfeatures.renderscript=false
android.defaults.buildfeatures.resvalues=true
android.defaults.buildfeatures.shaders=false
android.defaults.buildfeatures.databinding=false
android.defaults.buildfeatures.viewbinding=false


# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official

# https://kotlinlang.org/docs/reference/kapt.html#compile-avoidance-for-kapt-since-1320
kapt.include.compile.classpath=false

# https://blog.jetbrains.com/kotlin/2022/07/a-new-approach-to-incremental-compilation-in-kotlin/#how-to
kotlin.incremental.useClasspathSnapshot=true

###################################
## Application specific settings ##

targetSdk=33
compileSdk=34
compileSdkExtension=
minSdk=26

# compileSdkPreview must be a platform preview name (e.g. "T" or "U")
compileSdkPreview=

buildToolsVersion=34.0.0

# https://androidx.dev/snapshots/builds
androidXSnapshotBuildId=9346341

# https://r8.googlesource.com/r8/+log/refs/heads/4.0
r8Version=

# https://developer.android.com/studio/releases
androidGradlePluginVersion=8.1.0

kotlinVersion=1.9.0

gradleTestRetryVersion=1.5.2

androidCacheFixVersion=2.7.1

NDK_VERSION=25.1.8937393

build.kotlin.k2.enable=false
build.jetpack.compose.kotlinVersionCompatibilityCheck=true

build.timeout.kotlinCompile=15
build.timeout.unittest=20

# https://docs.gradle.org/current/dsl/org.gradle.api.tasks.testing.Test.html#org.gradle.api.tasks.testing.Test:maxParallelForks
line.unittest.maxforks=0
# https://docs.gradle.org/current/dsl/org.gradle.api.tasks.testing.Test.html#org.gradle.api.tasks.testing.Test:jvmArgs
# -Xss2m :
#  Our unittests need more stack size. Default stack size of JVM is 1m.
# -XX:+UseParallelGC :
#  This is needed to improve build performance
#  https://developer.android.com/studio/releases/gradle-plugin#optimize-gc-jdk-11
# -Djdk.attach.allowAttachSelf=true :
#  Avoid to raise java.lang.IllegalStateException
#  https://github.com/raphw/byte-buddy/issues/612#issuecomment-463618016
# --add-opens= :
#  Allow to access JVM internal APIs
#  https://docs.gradle.org/7.5/userguide/upgrading_version_7.html#removes_implicit_add_opens_for_test_workers
build.unittest.jvmargs=-Xss2m -XX:+UseParallelGC -Djdk.attach.allowAttachSelf=true --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED
# https://docs.gradle.org/current/dsl/org.gradle.api.tasks.testing.Test.html#org.gradle.api.tasks.testing.Test:forkEvery
build.unittest.forkevery=8
# https://docs.gradle.org/current/dsl/org.gradle.api.tasks.testing.Test.html#org.gradle.api.tasks.testing.Test:minHeapSize
build.unittest.heapsize.min=1g
# https://docs.gradle.org/current/dsl/org.gradle.api.tasks.testing.Test.html#org.gradle.api.tasks.testing.Test:maxHeapSize
build.unittest.heapsize.max=6g
# Print unittest log on console or Not
#build.unittest.logging=false

build.unittest.retry.max=3
build.unittest.retry.maxfailures=12
build.unittest.retry.treatasfail=false

